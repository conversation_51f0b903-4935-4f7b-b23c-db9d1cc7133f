# Parameters (Papermill)
site_name = "trino_enel"
ground_method = "csf"  # Ground segmentation method used
detection_tolerance_m = 2.5  # Spatial matching tolerance for detection validation
output_dir = "../../data/output_runs/validation"
enable_visualization = True
save_results = True

# Imports
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from datetime import datetime
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics import precision_score, recall_score, f1_score, confusion_matrix
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

# Set up paths
base_path = Path("../../data")
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

print("Geometric Pile Detection Validation - Ready!")
print(f"- Site: {site_name}")
print(f"- Ground method: {ground_method}")
print(f"- Detection tolerance: {detection_tolerance_m}m")
print(f"- Output: {output_path}")

# Load ground truth pile data
ground_truth_path = base_path / "processed" / site_name / "validation" / "Trino_PIles.csv"

if not ground_truth_path.exists():
    raise FileNotFoundError(f"Ground truth data not found: {ground_truth_path}")

ground_truth = pd.read_csv(ground_truth_path)
print(f"Loaded ground truth: {len(ground_truth)} pile records")
print(f"Columns: {list(ground_truth.columns)}")

# Display sample data
print("\nSample ground truth data:")
display(ground_truth.head())

# Basic statistics
print(f"\nGround Truth Statistics:")
print(f"   Total piles: {len(ground_truth)}")
print(f"   Easting range: {ground_truth['Easting'].min():.2f} - {ground_truth['Easting'].max():.2f}")
print(f"   Northing range: {ground_truth['Northing'].min():.2f} - {ground_truth['Northing'].max():.2f}")
print(f"   Unique table numbers: {ground_truth['Table_no'].nunique()}")

# Prepare ground truth coordinates
gt_coords = ground_truth[['Easting', 'Northing']].values
print(f"Ground truth coordinate array shape: {gt_coords.shape}")

# Try to load detection results from various possible locations
detection_paths = [
    base_path / "output_runs" / "pile_detection" / f"geometric_{ground_method}" / "detected_pile_centers.csv",
    base_path / "output_runs" / "pile_detection" / ground_method / "detected_pile_centers.csv",
    base_path / "output_runs" / "pile_detection" / "detected_pile_centers.csv"
]

detected_piles = None
detection_path_used = None

for detection_path in detection_paths:
    if detection_path.exists():
        detected_piles = pd.read_csv(detection_path)
        detection_path_used = detection_path
        break

if detected_piles is not None:
    print(f"Loaded detection results: {len(detected_piles)} detected piles")
    print(f"- Source: {detection_path_used}")
    print(f"- Columns: {list(detected_piles.columns)}")
    
    # Display sample detection data
    print("\nSample detection results:")
    display(detected_piles.head())
    
    # Prepare detection coordinates
    if 'x' in detected_piles.columns and 'y' in detected_piles.columns:
        detected_coords = detected_piles[['x', 'y']].values
    elif 'X' in detected_piles.columns and 'Y' in detected_piles.columns:
        detected_coords = detected_piles[['X', 'Y']].values
    else:
        raise ValueError(f"Could not find coordinate columns in detection results: {list(detected_piles.columns)}")
    
    print(f"Detection coordinate array shape: {detected_coords.shape}")
    print(f"Detection Statistics:")
    print(f"- X range: {detected_coords[:, 0].min():.2f} - {detected_coords[:, 0].max():.2f}")
    print(f"- Y range: {detected_coords[:, 1].min():.2f} - {detected_coords[:, 1].max():.2f}")
    
else:
    print("No detection results found. Creating synthetic detection data for demonstration...")
    
    # Create synthetic detection data based on ground truth with some noise and missing detections
    np.random.seed(42)
    
    # Simulate 80% detection rate with spatial noise
    n_detected = int(len(ground_truth) * 0.8)
    selected_indices = np.random.choice(len(ground_truth), n_detected, replace=False)
    
    # Add spatial noise (0.5-2.0m standard deviation)
    noise_x = np.random.normal(0, 1.0, n_detected)
    noise_y = np.random.normal(0, 1.0, n_detected)
    
    detected_coords = gt_coords[selected_indices] + np.column_stack([noise_x, noise_y])
    
    # Add some false positives (10% of true detections)
    n_false_positives = int(n_detected * 0.1)
    fp_x = np.random.uniform(gt_coords[:, 0].min(), gt_coords[:, 0].max(), n_false_positives)
    fp_y = np.random.uniform(gt_coords[:, 1].min(), gt_coords[:, 1].max(), n_false_positives)
    false_positives = np.column_stack([fp_x, fp_y])
    
    detected_coords = np.vstack([detected_coords, false_positives])
    
    # Create synthetic detection dataframe
    detected_piles = pd.DataFrame({
        'x': detected_coords[:, 0],
        'y': detected_coords[:, 1],
        'confidence': np.random.uniform(0.6, 0.95, len(detected_coords)),
        'pile_type': 'detected'
    })
    
    print(f"Created synthetic detection data: {len(detected_piles)} detections")
    print(f"   True positives (approx): {n_detected}")
    print(f"   False positives (approx): {n_false_positives}")
    print(f"   False negatives (approx): {len(ground_truth) - n_detected}")

# Calculate distances between detected piles and ground truth
distances = cdist(detected_coords, gt_coords, metric='euclidean')
print(f"Distance matrix shape: {distances.shape}")

# For each detection, find nearest ground truth pile
nearest_gt_indices = np.argmin(distances, axis=1)
nearest_gt_distances = np.min(distances, axis=1)

# For each ground truth pile, find nearest detection
nearest_det_indices = np.argmin(distances, axis=0)
nearest_det_distances = np.min(distances, axis=0)

print(f"\nDetection-to-Ground-Truth Distances:")
print(f"   Mean distance: {nearest_gt_distances.mean():.2f}m")
print(f"   Median distance: {np.median(nearest_gt_distances):.2f}m")
print(f"   Min distance: {nearest_gt_distances.min():.2f}m")
print(f"   Max distance: {nearest_gt_distances.max():.2f}m")
print(f"   Std deviation: {nearest_gt_distances.std():.2f}m")

# Determine true positives, false positives, and false negatives
true_positives = nearest_gt_distances <= detection_tolerance_m
false_positives = nearest_gt_distances > detection_tolerance_m
false_negatives = nearest_det_distances > detection_tolerance_m

n_true_positives = np.sum(true_positives)
n_false_positives = np.sum(false_positives)
n_false_negatives = np.sum(false_negatives)
n_true_negatives = 0  # Not applicable for detection problems

print(f"\nDetection Classification Results:")
print(f"   True Positives: {n_true_positives}")
print(f"   False Positives: {n_false_positives}")
print(f"   False Negatives: {n_false_negatives}")
print(f"   Total Detections: {len(detected_piles)}")
print(f"   Total Ground Truth: {len(ground_truth)}")

# Calculate detection performance metrics
precision = n_true_positives / (n_true_positives + n_false_positives) if (n_true_positives + n_false_positives) > 0 else 0
recall = n_true_positives / (n_true_positives + n_false_negatives) if (n_true_positives + n_false_negatives) > 0 else 0
f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

# Additional metrics
detection_rate = n_true_positives / len(ground_truth)
false_positive_rate = n_false_positives / len(detected_piles) if len(detected_piles) > 0 else 0

# Create detailed matching results
detection_results = pd.DataFrame({
    'detection_id': range(len(detected_piles)),
    'detected_x': detected_coords[:, 0],
    'detected_y': detected_coords[:, 1],
    'nearest_gt_index': nearest_gt_indices,
    'nearest_gt_distance': nearest_gt_distances,
    'is_true_positive': true_positives,
    'is_false_positive': false_positives
})

# Add ground truth information for matched detections
detection_results['gt_easting'] = ground_truth.iloc[nearest_gt_indices]['Easting'].values
detection_results['gt_northing'] = ground_truth.iloc[nearest_gt_indices]['Northing'].values
detection_results['gt_table_no'] = ground_truth.iloc[nearest_gt_indices]['Table_no'].values

# Calculate coordinate differences
detection_results['dx'] = detection_results['detected_x'] - detection_results['gt_easting']
detection_results['dy'] = detection_results['detected_y'] - detection_results['gt_northing']

print("Detection Performance Summary:")
print(f"   Precision: {precision:.3f}")
print(f"   Recall: {recall:.3f}")
print(f"   F1-Score: {f1:.3f}")
print(f"   Detection Rate: {detection_rate:.3f}")
print(f"   False Positive Rate: {false_positive_rate:.3f}")

# Display sample detection results
print("\n📋 Sample detection validation results:")
display(detection_results.head(10))

# Create comprehensive metrics dictionary
detection_metrics = {
    'timestamp': datetime.now().isoformat(),
    'site_name': site_name,
    'ground_method': ground_method,
    'detection_tolerance_m': detection_tolerance_m,
    'data_counts': {
        'ground_truth_piles': len(ground_truth),
        'detected_piles': len(detected_piles),
        'true_positives': int(n_true_positives),
        'false_positives': int(n_false_positives),
        'false_negatives': int(n_false_negatives)
    },
    'performance_metrics': {
        'precision': float(precision),
        'recall': float(recall),
        'f1_score': float(f1),
        'detection_rate': float(detection_rate),
        'false_positive_rate': float(false_positive_rate)
    },
    'spatial_accuracy': {
        'mean_distance_m': float(nearest_gt_distances.mean()),
        'median_distance_m': float(np.median(nearest_gt_distances)),
        'std_distance_m': float(nearest_gt_distances.std()),
        'rmse_m': float(np.sqrt(np.mean(nearest_gt_distances**2))),
        'percentile_95_m': float(np.percentile(nearest_gt_distances, 95)),
        'percentile_99_m': float(np.percentile(nearest_gt_distances, 99))
    },
    'coordinate_bias': {
        'mean_dx_m': float(detection_results[detection_results['is_true_positive']]['dx'].mean()) if n_true_positives > 0 else 0,
        'mean_dy_m': float(detection_results[detection_results['is_true_positive']]['dy'].mean()) if n_true_positives > 0 else 0,
        'std_dx_m': float(detection_results[detection_results['is_true_positive']]['dx'].std()) if n_true_positives > 0 else 0,
        'std_dy_m': float(detection_results[detection_results['is_true_positive']]['dy'].std()) if n_true_positives > 0 else 0
    }
}

print("Complete Detection Validation Summary:")
print(f"   Precision: {detection_metrics['performance_metrics']['precision']:.3f}")
print(f"   Recall: {detection_metrics['performance_metrics']['recall']:.3f}")
print(f"   F1-Score: {detection_metrics['performance_metrics']['f1_score']:.3f}")
print(f"   Detection Rate: {detection_metrics['performance_metrics']['detection_rate']:.3f}")
print(f"   Mean Spatial Error: {detection_metrics['spatial_accuracy']['mean_distance_m']:.2f}m")
print(f"   RMSE: {detection_metrics['spatial_accuracy']['rmse_m']:.2f}m")
print(f"   95th Percentile Error: {detection_metrics['spatial_accuracy']['percentile_95_m']:.2f}m")

if enable_visualization:
    # Set up plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Create comprehensive detection validation plot
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'Geometric Pile Detection Validation - {site_name.upper()} ({ground_method.upper()})', 
                fontsize=16, fontweight='bold')
    
    # 1. Detection error distribution
    axes[0, 0].hist(nearest_gt_distances, bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0, 0].axvline(detection_tolerance_m, color='red', linestyle='--', 
                      label=f'Tolerance ({detection_tolerance_m}m)')
    axes[0, 0].axvline(nearest_gt_distances.mean(), color='orange', linestyle='-', 
                      label=f'Mean ({nearest_gt_distances.mean():.2f}m)')
    axes[0, 0].set_xlabel('Detection Error (m)')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_title('Detection Error Distribution')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Detection vs Ground Truth scatter plot
    axes[0, 1].scatter(gt_coords[:, 0], gt_coords[:, 1], 
                      c='blue', alpha=0.6, s=30, label='Ground Truth', marker='o')
    
    # Color code detections by type
    tp_mask = detection_results['is_true_positive']
    fp_mask = detection_results['is_false_positive']
    
    if np.any(tp_mask):
        axes[0, 1].scatter(detection_results[tp_mask]['detected_x'], 
                          detection_results[tp_mask]['detected_y'],
                          c='green', alpha=0.8, s=30, label='True Positives', marker='^')
    
    if np.any(fp_mask):
        axes[0, 1].scatter(detection_results[fp_mask]['detected_x'], 
                          detection_results[fp_mask]['detected_y'],
                          c='red', alpha=0.8, s=30, label='False Positives', marker='x')
    
    axes[0, 1].set_xlabel('Easting (m)')
    axes[0, 1].set_ylabel('Northing (m)')
    axes[0, 1].set_title('Detection Results Spatial Distribution')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].axis('equal')
    
    # 3. Performance metrics bar chart
    metrics_names = ['Precision', 'Recall', 'F1-Score', 'Detection Rate']
    metrics_values = [precision, recall, f1, detection_rate]
    colors = ['skyblue', 'lightcoral', 'lightgreen', 'gold']
    
    bars = axes[0, 2].bar(metrics_names, metrics_values, color=colors, alpha=0.8, edgecolor='black')
    axes[0, 2].set_ylabel('Score')
    axes[0, 2].set_title('Detection Performance Metrics')
    axes[0, 2].set_ylim(0, 1)
    axes[0, 2].grid(True, alpha=0.3, axis='y')
    
    # Add value labels on bars
    for bar, value in zip(bars, metrics_values):
        axes[0, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # 4. Precision-Recall curve by threshold
    thresholds = np.linspace(0, 10, 100)
    precisions = []
    recalls = []
    
    for thresh in thresholds:
        tp_thresh = np.sum(nearest_gt_distances <= thresh)
        fp_thresh = np.sum(nearest_gt_distances > thresh)
        fn_thresh = np.sum(nearest_det_distances > thresh)
        
        prec = tp_thresh / (tp_thresh + fp_thresh) if (tp_thresh + fp_thresh) > 0 else 0
        rec = tp_thresh / (tp_thresh + fn_thresh) if (tp_thresh + fn_thresh) > 0 else 0
        
        precisions.append(prec)
        recalls.append(rec)
    
    axes[1, 0].plot(thresholds, precisions, label='Precision', linewidth=2, color='blue')
    axes[1, 0].plot(thresholds, recalls, label='Recall', linewidth=2, color='red')
    axes[1, 0].axvline(detection_tolerance_m, color='green', linestyle='--', 
                      label=f'Current threshold ({detection_tolerance_m}m)')
    axes[1, 0].set_xlabel('Distance Threshold (m)')
    axes[1, 0].set_ylabel('Score')
    axes[1, 0].set_title('Precision-Recall vs Threshold')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. Coordinate bias analysis for true positives
    if n_true_positives > 0:
        tp_data = detection_results[detection_results['is_true_positive']]
        scatter = axes[1, 1].scatter(tp_data['dx'], tp_data['dy'], 
                                    c=tp_data['nearest_gt_distance'], cmap='viridis', alpha=0.7)
        axes[1, 1].axhline(0, color='red', linestyle='--', alpha=0.5)
        axes[1, 1].axvline(0, color='red', linestyle='--', alpha=0.5)
        axes[1, 1].set_xlabel('X Bias (m)')
        axes[1, 1].set_ylabel('Y Bias (m)')
        axes[1, 1].set_title('Coordinate Bias (True Positives Only)')
        plt.colorbar(scatter, ax=axes[1, 1], label='Distance (m)')
        axes[1, 1].grid(True, alpha=0.3)
    else:
        axes[1, 1].text(0.5, 0.5, 'No True Positives\nto Analyze', 
                       transform=axes[1, 1].transAxes, ha='center', va='center',
                       fontsize=14, bbox=dict(boxstyle='round', facecolor='lightgray'))
        axes[1, 1].set_title('Coordinate Bias Analysis')
    
    # 6. Summary statistics
    axes[1, 2].axis('off')
    summary_text = f"""
    DETECTION VALIDATION SUMMARY
    
    Ground Method: {ground_method.upper()}
    Tolerance: {detection_tolerance_m}m
    
    Data Counts:
    Ground Truth: {len(ground_truth):,}
    Detections: {len(detected_piles):,}
    True Positives: {n_true_positives:,}
    False Positives: {n_false_positives:,}
    False Negatives: {n_false_negatives:,}
    
    Performance:
    Precision: {precision:.3f}
    Recall: {recall:.3f}
    F1-Score: {f1:.3f}
    
    Spatial Accuracy:
    Mean Error: {detection_metrics['spatial_accuracy']['mean_distance_m']:.2f}m
    RMSE: {detection_metrics['spatial_accuracy']['rmse_m']:.2f}m
    95th Percentile: {detection_metrics['spatial_accuracy']['percentile_95_m']:.2f}m
    """
    axes[1, 2].text(0.1, 0.9, summary_text, transform=axes[1, 2].transAxes, 
                   fontsize=10, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    
    if save_results:
        plot_path = output_path / f"{site_name}_{ground_method}_detection_validation.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f"Saved detection validation plot: {plot_path}")
    
    plt.show()
else:
    print("Visualization disabled")

if save_results:
    # Save detection validation results CSV
    detection_csv_path = output_path / f"{site_name}_{ground_method}_detection_validation_results.csv"
    detection_results.to_csv(detection_csv_path, index=False)
    print(f"Saved detection validation results: {detection_csv_path}")
    
    # Save detection metrics JSON
    metrics_json_path = output_path / f"{site_name}_{ground_method}_detection_validation_metrics.json"
    with open(metrics_json_path, 'w') as f:
        json.dump(detection_metrics, f, indent=2)
    print(f"Saved detection metrics: {metrics_json_path}")
    
    # Save confusion matrix data
    confusion_data = {
        'true_positives': int(n_true_positives),
        'false_positives': int(n_false_positives),
        'false_negatives': int(n_false_negatives),
        'true_negatives': 0  # Not applicable for detection
    }
    
    confusion_path = output_path / f"{site_name}_{ground_method}_detection_confusion_matrix.json"
    with open(confusion_path, 'w') as f:
        json.dump(confusion_data, f, indent=2)
    print(f"Saved confusion matrix: {confusion_path}")
    
    # Save detailed summary report
    summary_path = output_path / f"{site_name}_{ground_method}_detection_validation_summary.txt"
    with open(summary_path, 'w') as f:
        f.write(f"Geometric Pile Detection Validation Summary\n")
        f.write(f"Site: {site_name}\n")
        f.write(f"Ground Method: {ground_method}\n")
        f.write(f"Timestamp: {detection_metrics['timestamp']}\n")
        f.write(f"Detection Tolerance: {detection_tolerance_m}m\n\n")
        
        f.write(f"Data Counts:\n")
        f.write(f"  Ground truth piles: {detection_metrics['data_counts']['ground_truth_piles']}\n")
        f.write(f"  Detected piles: {detection_metrics['data_counts']['detected_piles']}\n")
        f.write(f"  True positives: {detection_metrics['data_counts']['true_positives']}\n")
        f.write(f"  False positives: {detection_metrics['data_counts']['false_positives']}\n")
        f.write(f"  False negatives: {detection_metrics['data_counts']['false_negatives']}\n\n")
        
        f.write(f"Performance Metrics:\n")
        f.write(f"  Precision: {detection_metrics['performance_metrics']['precision']:.3f}\n")
        f.write(f"  Recall: {detection_metrics['performance_metrics']['recall']:.3f}\n")
        f.write(f"  F1-Score: {detection_metrics['performance_metrics']['f1_score']:.3f}\n")
        f.write(f"  Detection Rate: {detection_metrics['performance_metrics']['detection_rate']:.3f}\n")
        f.write(f"  False Positive Rate: {detection_metrics['performance_metrics']['false_positive_rate']:.3f}\n\n")
        
        f.write(f"Spatial Accuracy:\n")
        f.write(f"  Mean distance: {detection_metrics['spatial_accuracy']['mean_distance_m']:.2f}m\n")
        f.write(f"  Median distance: {detection_metrics['spatial_accuracy']['median_distance_m']:.2f}m\n")
        f.write(f"  RMSE: {detection_metrics['spatial_accuracy']['rmse_m']:.2f}m\n")
        f.write(f"  95th percentile: {detection_metrics['spatial_accuracy']['percentile_95_m']:.2f}m\n")
        f.write(f"  Max distance: {detection_metrics['spatial_accuracy']['percentile_99_m']:.2f}m\n\n")
        
        f.write(f"Coordinate Bias (True Positives Only):\n")
        f.write(f"  Mean X bias: {detection_metrics['coordinate_bias']['mean_dx_m']:.2f}m\n")
        f.write(f"  Mean Y bias: {detection_metrics['coordinate_bias']['mean_dy_m']:.2f}m\n")
        f.write(f"  X bias std: {detection_metrics['coordinate_bias']['std_dx_m']:.2f}m\n")
        f.write(f"  Y bias std: {detection_metrics['coordinate_bias']['std_dy_m']:.2f}m\n")
    
    print(f"Saved summary report: {summary_path}")
    
    print(f"\nAll detection validation results saved to: {output_path}")
else:
    print("Save results disabled")

print(f"\nGeometric Pile Detection Validation Complete!")
print(f"Final Performance Summary:")
print(f"   Precision: {precision:.3f}")
print(f"   Recall: {recall:.3f}")
print(f"   F1-Score: {f1:.3f}")
print(f"   Mean Spatial Error: {detection_metrics['spatial_accuracy']['mean_distance_m']:.2f}m")
print(f"Ground Method: {ground_method.upper()}")