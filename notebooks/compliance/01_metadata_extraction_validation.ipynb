{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Metadata Extraction Validation\n", "\n", "This notebook validates IFC metadata extraction results against the Trino_PIles.csv ground truth data.\n", "\n", "**Purpose**: Validate IFC pile metadata extraction accuracy and spatial coordinate precision  \n", "**Input**: IFC metadata CSV + Trino_PIles.csv ground truth  \n", "**Output**: Validation metrics, coordinate accuracy assessment, and discrepancy analysis  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "site_name = \"trino_enel\"\n", "validation_tolerance_m = 2.0  # Spatial matching tolerance in meters\n", "output_dir = \"../../data/output_runs/validation\"\n", "enable_visualization = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metadata Extraction Validation - Ready!\n", "- Site: trino_enel\n", "- Tolerance: 2.0m\n", "- Output: ../../data/output_runs/validation\n"]}], "source": ["# Imports\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "from sklearn.neighbors import NearestNeighbors\n", "from scipy.spatial.distance import cdist\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up paths\n", "base_path = Path(\"../../data\")\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"Metadata Extraction Validation - Ready!\")\n", "print(f\"- Site: {site_name}\")\n", "print(f\"- Tolerance: {validation_tolerance_m}m\")\n", "print(f\"- Output: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Ground Truth Data\n", "\n", "Load the Trino_PIles.csv validation data as ground truth reference."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded ground truth: 14599 pile records\n", "Columns: ['S.No', 'Table_no', 'Easting', 'Northing']\n", "\n", "Sample ground truth data:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON>.No</th>\n", "      <th>Table_no</th>\n", "      <th>Easting</th>\n", "      <th><PERSON>ing</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Row 188/Array 1763</td>\n", "      <td>435896.1694</td>\n", "      <td>5011304.321</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Row 187/Array 1744</td>\n", "      <td>435886.6714</td>\n", "      <td>5011296.815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Row 188/Array 1763</td>\n", "      <td>435896.2065</td>\n", "      <td>5011296.794</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Row 186/Array 1725</td>\n", "      <td>435877.2498</td>\n", "      <td>5011289.097</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>Row 187/Array 1744</td>\n", "      <td>435886.6335</td>\n", "      <td>5011289.201</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   S.No            Table_no      Easting     Northing\n", "0     1  Row 188/Array 1763  435896.1694  5011304.321\n", "1     2  Row 187/Array 1744  435886.6714  5011296.815\n", "2     3  Row 188/Array 1763  435896.2065  5011296.794\n", "3     4  Row 186/Array 1725  435877.2498  5011289.097\n", "4     5  Row 187/Array 1744  435886.6335  5011289.201"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Ground Truth Statistics:\n", "   Total piles: 14599\n", "   Easting range: 435267.20 - 436719.95\n", "   Northing range: 5010903.34 - 5012460.90\n", "   Unique table numbers: 7143\n"]}], "source": ["# Load ground truth pile data\n", "ground_truth_path = base_path / \"processed\" / site_name / \"validation\" / \"Trino_PIles.csv\"\n", "\n", "if not ground_truth_path.exists():\n", "    raise FileNotFoundError(f\"Ground truth data not found: {ground_truth_path}\")\n", "\n", "ground_truth = pd.read_csv(ground_truth_path)\n", "print(f\"Loaded ground truth: {len(ground_truth)} pile records\")\n", "print(f\"Columns: {list(ground_truth.columns)}\")\n", "\n", "# Display sample data\n", "print(\"\\nSample ground truth data:\")\n", "display(ground_truth.head())\n", "\n", "# Basic statistics\n", "print(f\"\\nGround Truth Statistics:\")\n", "print(f\"   Total piles: {len(ground_truth)}\")\n", "print(f\"   Easting range: {ground_truth['Easting'].min():.2f} - {ground_truth['Easting'].max():.2f}\")\n", "print(f\"   Northing range: {ground_truth['Northing'].min():.2f} - {ground_truth['Northing'].max():.2f}\")\n", "print(f\"   Unique table numbers: {ground_truth['Table_no'].nunique()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load IFC Metadata\n", "\n", "Load the extracted IFC pile metadata for comparison."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded IFC metadata: 14460 pile records\n", "Columns: ['GlobalId', 'Name', 'Tag', 'Type', 'X', 'Y', 'Z', 'Latitude', 'Longitude', 'Properties']\n", "\n", "Sample IFC metadata:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>GlobalId</th>\n", "      <th>Name</th>\n", "      <th>Tag</th>\n", "      <th>Type</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Z</th>\n", "      <th>Latitude</th>\n", "      <th>Longitude</th>\n", "      <th>Properties</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1u7AZf3On2lwsljDdawZWm</td>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:952577</td>\n", "      <td>952577</td>\n", "      <td>IfcColumn</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012179e+06</td>\n", "      <td>160.830786</td>\n", "      <td>45.260173</td>\n", "      <td>8.181110</td>\n", "      <td>{'Pset_ColumnCommon': {'Reference': 'TRPL_Trac...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1u7AZf3On2lwsljDdawZWp</td>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:952578</td>\n", "      <td>952578</td>\n", "      <td>IfcColumn</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012187e+06</td>\n", "      <td>160.830786</td>\n", "      <td>45.260248</td>\n", "      <td>8.181109</td>\n", "      <td>{'Pset_ColumnCommon': {'Reference': 'TRPL_Trac...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1u7AZf3On2lwsljDdawZWo</td>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:952579</td>\n", "      <td>952579</td>\n", "      <td>IfcColumn</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012196e+06</td>\n", "      <td>160.830786</td>\n", "      <td>45.260323</td>\n", "      <td>8.181107</td>\n", "      <td>{'Pset_ColumnCommon': {'Reference': 'TRPL_Trac...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1u7AZf3On2lwsljDdawZWr</td>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:952580</td>\n", "      <td>952580</td>\n", "      <td>IfcColumn</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012171e+06</td>\n", "      <td>160.830786</td>\n", "      <td>45.260099</td>\n", "      <td>8.181111</td>\n", "      <td>{'Pset_ColumnCommon': {'Reference': 'TRPL_Trac...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1u7AZf3On2lwsljDdawZWq</td>\n", "      <td>TRPL_Tracker Pile:TRPL_Tracker Pile:952581</td>\n", "      <td>952581</td>\n", "      <td>IfcColumn</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012204e+06</td>\n", "      <td>160.830786</td>\n", "      <td>45.260397</td>\n", "      <td>8.181106</td>\n", "      <td>{'Pset_ColumnCommon': {'Reference': 'TRPL_Trac...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 GlobalId                                        Name     Tag  \\\n", "0  1u7AZf3On2lwsljDdawZWm  TRPL_Tracker Pile:TRPL_Tracker Pile:952577  952577   \n", "1  1u7AZf3On2lwsljDdawZWp  TRPL_Tracker Pile:TRPL_Tracker Pile:952578  952578   \n", "2  1u7AZf3On2lwsljDdawZWo  TRPL_Tracker Pile:TRPL_Tracker Pile:952579  952579   \n", "3  1u7AZf3On2lwsljDdawZWr  TRPL_Tracker Pile:TRPL_Tracker Pile:952580  952580   \n", "4  1u7AZf3On2lwsljDdawZWq  TRPL_Tracker Pile:TRPL_Tracker Pile:952581  952581   \n", "\n", "        Type              X             Y           Z   Latitude  Longitude  \\\n", "0  IfcColumn  435751.683957  5.012179e+06  160.830786  45.260173   8.181110   \n", "1  IfcColumn  435751.683957  5.012187e+06  160.830786  45.260248   8.181109   \n", "2  IfcColumn  435751.683957  5.012196e+06  160.830786  45.260323   8.181107   \n", "3  IfcColumn  435751.683957  5.012171e+06  160.830786  45.260099   8.181111   \n", "4  IfcColumn  435751.683957  5.012204e+06  160.830786  45.260397   8.181106   \n", "\n", "                                          Properties  \n", "0  {'Pset_ColumnCommon': {'Reference': 'TRPL_Trac...  \n", "1  {'Pset_ColumnCommon': {'Reference': 'TRPL_Trac...  \n", "2  {'Pset_ColumnCommon': {'Reference': 'TRPL_Trac...  \n", "3  {'Pset_ColumnCommon': {'Reference': 'TRPL_Trac...  \n", "4  {'Pset_ColumnCommon': {'Reference': 'TRPL_Trac...  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "IFC Metadata Statistics:\n", "   Total piles: 14460\n", "   X range: 435267.20 - 436719.95\n", "   Y range: 5010900.71 - 5012462.41\n", "   Z range: 157.13 - 161.66\n", "   Unique tags: 14460\n"]}], "source": ["# Load IFC metadata\n", "ifc_metadata_path = base_path / \"processed\" / site_name / \"advanced_ifc_metadata\" / \"advanced_tracker_piles.csv\"\n", "\n", "if not ifc_metadata_path.exists():\n", "    raise FileNotFoundError(f\"IFC metadata not found: {ifc_metadata_path}\")\n", "\n", "ifc_metadata = pd.read_csv(ifc_metadata_path)\n", "print(f\"Loaded IFC metadata: {len(ifc_metadata)} pile records\")\n", "print(f\"Columns: {list(ifc_metadata.columns)}\")\n", "\n", "# Display sample data\n", "print(\"\\nSample IFC metadata:\")\n", "display(ifc_metadata.head())\n", "\n", "# Basic statistics\n", "print(f\"\\nIFC Metadata Statistics:\")\n", "print(f\"   Total piles: {len(ifc_metadata)}\")\n", "print(f\"   X range: {ifc_metadata['X'].min():.2f} - {ifc_metadata['X'].max():.2f}\")\n", "print(f\"   Y range: {ifc_metadata['Y'].min():.2f} - {ifc_metadata['Y'].max():.2f}\")\n", "print(f\"   Z range: {ifc_metadata['Z'].min():.2f} - {ifc_metadata['Z'].max():.2f}\")\n", "print(f\"   Unique tags: {ifc_metadata['Tag'].nunique()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Spatial Matching Analysis\n", "\n", "Match IFC pile coordinates with ground truth coordinates using nearest neighbor analysis."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ground truth coordinates: (14599, 2)\n", "IFC coordinates: (14460, 2)\n", "Distance matrix shape: (14460, 14599)\n", "\n", "Distance Statistics:\n", "   Mean distance: 1.53m\n", "   Median distance: 1.62m\n", "   Min distance: 0.00m\n", "   Max distance: 10.27m\n", "   Std deviation: 0.82m\n"]}], "source": ["# Prepare coordinate arrays for matching\n", "ground_truth_coords = ground_truth[['Easting', 'Northing']].values\n", "ifc_coords = ifc_metadata[['X', 'Y']].values\n", "\n", "print(f\"Ground truth coordinates: {ground_truth_coords.shape}\")\n", "print(f\"IFC coordinates: {ifc_coords.shape}\")\n", "\n", "# Calculate pairwise distances\n", "distances = cdist(ifc_coords, ground_truth_coords, metric='euclidean')\n", "print(f\"Distance matrix shape: {distances.shape}\")\n", "\n", "# Find nearest neighbors for each IFC pile\n", "nearest_indices = np.argmin(distances, axis=1)\n", "nearest_distances = np.min(distances, axis=1)\n", "\n", "print(f\"\\nDistance Statistics:\")\n", "print(f\"   Mean distance: {nearest_distances.mean():.2f}m\")\n", "print(f\"   Median distance: {np.median(nearest_distances):.2f}m\")\n", "print(f\"   Min distance: {nearest_distances.min():.2f}m\")\n", "print(f\"   Max distance: {nearest_distances.max():.2f}m\")\n", "print(f\"   Std deviation: {nearest_distances.std():.2f}m\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created matching results: 14460 records\n", "Matches within tolerance (2.0m): 10937\n", "Match rate: 75.6%\n", "\n", "Sample matching results:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ifc_index</th>\n", "      <th>ifc_tag</th>\n", "      <th>ifc_x</th>\n", "      <th>ifc_y</th>\n", "      <th>ifc_z</th>\n", "      <th>gt_index</th>\n", "      <th>gt_s_no</th>\n", "      <th>gt_table_no</th>\n", "      <th>gt_easting</th>\n", "      <th>gt_northing</th>\n", "      <th>distance_m</th>\n", "      <th>within_tolerance</th>\n", "      <th>dx</th>\n", "      <th>dy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>952577</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012179e+06</td>\n", "      <td>160.830786</td>\n", "      <td>11253</td>\n", "      <td>11254</td>\n", "      <td>2496.02</td>\n", "      <td>435751.6837</td>\n", "      <td>5012180.027</td>\n", "      <td>0.87552</td>\n", "      <td>True</td>\n", "      <td>0.000257</td>\n", "      <td>-0.87552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>952578</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012187e+06</td>\n", "      <td>160.830786</td>\n", "      <td>11254</td>\n", "      <td>11255</td>\n", "      <td>2496.03</td>\n", "      <td>435751.6837</td>\n", "      <td>5012187.524</td>\n", "      <td>0.08002</td>\n", "      <td>True</td>\n", "      <td>0.000257</td>\n", "      <td>-0.08002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>952579</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012196e+06</td>\n", "      <td>160.830786</td>\n", "      <td>11255</td>\n", "      <td>11256</td>\n", "      <td>2496.04</td>\n", "      <td>435751.6837</td>\n", "      <td>5012195.022</td>\n", "      <td>0.71448</td>\n", "      <td>True</td>\n", "      <td>0.000257</td>\n", "      <td>0.71448</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>952580</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012171e+06</td>\n", "      <td>160.830786</td>\n", "      <td>11252</td>\n", "      <td>11253</td>\n", "      <td>2496.01</td>\n", "      <td>435751.6837</td>\n", "      <td>5012172.529</td>\n", "      <td>1.67002</td>\n", "      <td>True</td>\n", "      <td>0.000257</td>\n", "      <td>-1.67002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>952581</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012204e+06</td>\n", "      <td>160.830786</td>\n", "      <td>11256</td>\n", "      <td>11257</td>\n", "      <td>2496.05</td>\n", "      <td>435751.6837</td>\n", "      <td>5012202.520</td>\n", "      <td>1.50898</td>\n", "      <td>True</td>\n", "      <td>0.000257</td>\n", "      <td>1.50898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>5</td>\n", "      <td>952583</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012145e+06</td>\n", "      <td>160.883925</td>\n", "      <td>11258</td>\n", "      <td>11259</td>\n", "      <td>2497.02</td>\n", "      <td>435751.6837</td>\n", "      <td>5012146.221</td>\n", "      <td>0.88252</td>\n", "      <td>True</td>\n", "      <td>0.000257</td>\n", "      <td>-0.88252</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>6</td>\n", "      <td>952584</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012154e+06</td>\n", "      <td>160.883925</td>\n", "      <td>11259</td>\n", "      <td>11260</td>\n", "      <td>2497.03</td>\n", "      <td>435751.6837</td>\n", "      <td>5012153.720</td>\n", "      <td>0.08902</td>\n", "      <td>True</td>\n", "      <td>0.000257</td>\n", "      <td>-0.08902</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>7</td>\n", "      <td>952585</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012162e+06</td>\n", "      <td>160.883925</td>\n", "      <td>11260</td>\n", "      <td>11261</td>\n", "      <td>2497.04</td>\n", "      <td>435751.6837</td>\n", "      <td>5012161.218</td>\n", "      <td>0.70548</td>\n", "      <td>True</td>\n", "      <td>0.000257</td>\n", "      <td>0.70548</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>8</td>\n", "      <td>952586</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012137e+06</td>\n", "      <td>160.883925</td>\n", "      <td>11257</td>\n", "      <td>11258</td>\n", "      <td>2497.01</td>\n", "      <td>435751.6837</td>\n", "      <td>5012138.723</td>\n", "      <td>1.67702</td>\n", "      <td>True</td>\n", "      <td>0.000257</td>\n", "      <td>-1.67702</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>9</td>\n", "      <td>952587</td>\n", "      <td>435751.683957</td>\n", "      <td>5.012170e+06</td>\n", "      <td>160.883925</td>\n", "      <td>11261</td>\n", "      <td>11262</td>\n", "      <td>2497.05</td>\n", "      <td>435751.6837</td>\n", "      <td>5012168.717</td>\n", "      <td>1.49898</td>\n", "      <td>True</td>\n", "      <td>0.000257</td>\n", "      <td>1.49898</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   ifc_index  ifc_tag          ifc_x         ifc_y       ifc_z  gt_index  \\\n", "0          0   952577  435751.683957  5.012179e+06  160.830786     11253   \n", "1          1   952578  435751.683957  5.012187e+06  160.830786     11254   \n", "2          2   952579  435751.683957  5.012196e+06  160.830786     11255   \n", "3          3   952580  435751.683957  5.012171e+06  160.830786     11252   \n", "4          4   952581  435751.683957  5.012204e+06  160.830786     11256   \n", "5          5   952583  435751.683957  5.012145e+06  160.883925     11258   \n", "6          6   952584  435751.683957  5.012154e+06  160.883925     11259   \n", "7          7   952585  435751.683957  5.012162e+06  160.883925     11260   \n", "8          8   952586  435751.683957  5.012137e+06  160.883925     11257   \n", "9          9   952587  435751.683957  5.012170e+06  160.883925     11261   \n", "\n", "   gt_s_no gt_table_no   gt_easting  gt_northing  distance_m  \\\n", "0    11254     2496.02  435751.6837  5012180.027     0.87552   \n", "1    11255     2496.03  435751.6837  5012187.524     0.08002   \n", "2    11256     2496.04  435751.6837  5012195.022     0.71448   \n", "3    11253     2496.01  435751.6837  5012172.529     1.67002   \n", "4    11257     2496.05  435751.6837  5012202.520     1.50898   \n", "5    11259     2497.02  435751.6837  5012146.221     0.88252   \n", "6    11260     2497.03  435751.6837  5012153.720     0.08902   \n", "7    11261     2497.04  435751.6837  5012161.218     0.70548   \n", "8    11258     2497.01  435751.6837  5012138.723     1.67702   \n", "9    11262     2497.05  435751.6837  5012168.717     1.49898   \n", "\n", "   within_tolerance        dx       dy  \n", "0              True  0.000257 -0.87552  \n", "1              True  0.000257 -0.08002  \n", "2              True  0.000257  0.71448  \n", "3              True  0.000257 -1.67002  \n", "4              True  0.000257  1.50898  \n", "5              True  0.000257 -0.88252  \n", "6              True  0.000257 -0.08902  \n", "7              True  0.000257  0.70548  \n", "8              True  0.000257 -1.67702  \n", "9              True  0.000257  1.49898  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create matching results dataframe\n", "matching_results = pd.DataFrame({\n", "    'ifc_index': range(len(ifc_metadata)),\n", "    'ifc_tag': ifc_metadata['Tag'].values,\n", "    'ifc_x': ifc_metadata['X'].values,\n", "    'ifc_y': ifc_metadata['Y'].values,\n", "    'ifc_z': ifc_metadata['Z'].values,\n", "    'gt_index': nearest_indices,\n", "    'gt_s_no': ground_truth.iloc[nearest_indices]['S.No'].values,\n", "    'gt_table_no': ground_truth.iloc[nearest_indices]['Table_no'].values,\n", "    'gt_easting': ground_truth.iloc[nearest_indices]['Easting'].values,\n", "    'gt_northing': ground_truth.iloc[nearest_indices]['Northing'].values,\n", "    'distance_m': nearest_distances,\n", "    'within_tolerance': nearest_distances <= validation_tolerance_m\n", "})\n", "\n", "# Calculate coordinate differences\n", "matching_results['dx'] = matching_results['ifc_x'] - matching_results['gt_easting']\n", "matching_results['dy'] = matching_results['ifc_y'] - matching_results['gt_northing']\n", "\n", "print(f\"Created matching results: {len(matching_results)} records\")\n", "print(f\"Matches within tolerance ({validation_tolerance_m}m): {matching_results['within_tolerance'].sum()}\")\n", "print(f\"Match rate: {matching_results['within_tolerance'].mean()*100:.1f}%\")\n", "\n", "# Display sample matching results\n", "print(\"\\nSample matching results:\")\n", "display(matching_results.head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Validation Metrics\n", "\n", "Calculate comprehensive validation metrics for the metadata extraction."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Validation Metrics Summary:\n", "   Ground truth piles: 14599\n", "   IFC metadata piles: 14460\n", "   Match rate: 75.6%\n", "   Mean spatial error: 1.53m\n", "   RMSE: 1.74m\n", "   95th percentile error: 2.66m\n", "   Coordinate bias (X): -0.69m\n", "   Coordinate bias (Y): -0.07m\n"]}], "source": ["# Calculate validation metrics\n", "validation_metrics = {\n", "    'timestamp': datetime.now().isoformat(),\n", "    'site_name': site_name,\n", "    'validation_tolerance_m': validation_tolerance_m,\n", "    'ground_truth_count': len(ground_truth),\n", "    'ifc_metadata_count': len(ifc_metadata),\n", "    'matched_pairs': len(matching_results),\n", "    'matches_within_tolerance': int(matching_results['within_tolerance'].sum()),\n", "    'match_rate_percent': float(matching_results['within_tolerance'].mean() * 100),\n", "    'spatial_accuracy': {\n", "        'mean_distance_m': float(nearest_distances.mean()),\n", "        'median_distance_m': float(np.median(nearest_distances)),\n", "        'std_distance_m': float(nearest_distances.std()),\n", "        'min_distance_m': float(nearest_distances.min()),\n", "        'max_distance_m': float(nearest_distances.max()),\n", "        'rmse_m': float(np.sqrt(np.mean(nearest_distances**2))),\n", "        'percentile_95_m': float(np.percentile(nearest_distances, 95)),\n", "        'percentile_99_m': float(np.percentile(nearest_distances, 99))\n", "    },\n", "    'coordinate_bias': {\n", "        'mean_dx_m': float(matching_results['dx'].mean()),\n", "        'mean_dy_m': float(matching_results['dy'].mean()),\n", "        'std_dx_m': float(matching_results['dx'].std()),\n", "        'std_dy_m': float(matching_results['dy'].std())\n", "    }\n", "}\n", "\n", "print(\"Validation Metrics Summary:\")\n", "print(f\"   Ground truth piles: {validation_metrics['ground_truth_count']}\")\n", "print(f\"   IFC metadata piles: {validation_metrics['ifc_metadata_count']}\")\n", "print(f\"   Match rate: {validation_metrics['match_rate_percent']:.1f}%\")\n", "print(f\"   Mean spatial error: {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}m\")\n", "print(f\"   RMSE: {validation_metrics['spatial_accuracy']['rmse_m']:.2f}m\")\n", "print(f\"   95th percentile error: {validation_metrics['spatial_accuracy']['percentile_95_m']:.2f}m\")\n", "print(f\"   Coordinate bias (X): {validation_metrics['coordinate_bias']['mean_dx_m']:.2f}m\")\n", "print(f\"   Coordinate bias (Y): {validation_metrics['coordinate_bias']['mean_dy_m']:.2f}m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualization\n", "\n", "Create visualizations to analyze the validation results."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved validation plot: ../../data/output_runs/validation/trino_enel_metadata_validation_analysis.png\n"]}, {"data": {"image/png": "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*****************************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", "text/plain": ["<Figure size 1800x1200 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if enable_visualization:\n", "    # Set up the plotting style\n", "    plt.style.use('default')\n", "    sns.set_palette(\"husl\")\n", "    \n", "    # Create a comprehensive validation plot\n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    fig.suptitle(f'Metadata Extraction Validation - {site_name.upper()}', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. Distance distribution\n", "    axes[0, 0].hist(nearest_distances, bins=50, alpha=0.7, color='skyblue', edgecolor='black')\n", "    axes[0, 0].axvline(validation_tolerance_m, color='red', linestyle='--', label=f'Tolerance ({validation_tolerance_m}m)')\n", "    axes[0, 0].axvline(nearest_distances.mean(), color='orange', linestyle='-', label=f'Mean ({nearest_distances.mean():.2f}m)')\n", "    axes[0, 0].set_xlabel('Distance (m)')\n", "    axes[0, 0].set_ylabel('Frequency')\n", "    axes[0, 0].set_title('Distance Distribution')\n", "    axes[0, 0].legend()\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. Coordinate scatter plot\n", "    scatter = axes[0, 1].scatter(ground_truth['Easting'], ground_truth['Northing'], \n", "                                c='blue', alpha=0.6, s=20, label='Ground Truth')\n", "    axes[0, 1].scatter(ifc_metadata['X'], ifc_metadata['Y'], \n", "                      c='red', alpha=0.6, s=20, label='IFC Metadata')\n", "    axes[0, 1].set_xlabel('Easting (m)')\n", "    axes[0, 1].set_ylabel('Northing (m)')\n", "    axes[0, 1].set_title('Spatial Distribution Comparison')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    axes[0, 1].axis('equal')\n", "    \n", "    # 3. Coordinate bias analysis\n", "    axes[0, 2].scatter(matching_results['dx'], matching_results['dy'], \n", "                      c=matching_results['distance_m'], cmap='viridis', alpha=0.7)\n", "    axes[0, 2].axhline(0, color='red', linestyle='--', alpha=0.5)\n", "    axes[0, 2].axvline(0, color='red', linestyle='--', alpha=0.5)\n", "    axes[0, 2].set_xlabel('X Bias (m)')\n", "    axes[0, 2].set_ylabel('Y Bias (m)')\n", "    axes[0, 2].set_title('Coordinate Bias Analysis')\n", "    axes[0, 2].grid(True, alpha=0.3)\n", "    \n", "    # 4. Match rate by distance threshold\n", "    thresholds = np.linspace(0, 10, 100)\n", "    match_rates = [np.mean(nearest_distances <= t) * 100 for t in thresholds]\n", "    axes[1, 0].plot(thresholds, match_rates, linewidth=2, color='green')\n", "    axes[1, 0].axvline(validation_tolerance_m, color='red', linestyle='--', \n", "                      label=f'Current tolerance ({validation_tolerance_m}m)')\n", "    axes[1, 0].set_xlabel('Distance Threshold (m)')\n", "    axes[1, 0].set_ylabel('Match Rate (%)')\n", "    axes[1, 0].set_title('Match Rate vs Distance Threshold')\n", "    axes[1, 0].legend()\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # 5. Distance vs IFC tag (sample)\n", "    sample_size = min(100, len(matching_results))\n", "    sample_data = matching_results.sample(sample_size)\n", "    axes[1, 1].scatter(range(len(sample_data)), sample_data['distance_m'], alpha=0.7)\n", "    axes[1, 1].axhline(validation_tolerance_m, color='red', linestyle='--', alpha=0.7)\n", "    axes[1, 1].set_xlabel('Sample Index')\n", "    axes[1, 1].set_ylabel('Distance (m)')\n", "    axes[1, 1].set_title(f'Distance Distribution (Sample of {sample_size})')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    # 6. Summary statistics\n", "    axes[1, 2].axis('off')\n", "    summary_text = f\"\"\"\n", "    VALIDATION SUMMARY\n", "    \n", "    Ground Truth Piles: {len(ground_truth):,}\n", "    IFC Metadata Piles: {len(ifc_metadata):,}\n", "    \n", "    Match Rate: {validation_metrics['match_rate_percent']:.1f}%\n", "    Mean Error: {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}m\n", "    RMSE: {validation_metrics['spatial_accuracy']['rmse_m']:.2f}m\n", "    95th Percentile: {validation_metrics['spatial_accuracy']['percentile_95_m']:.2f}m\n", "    \n", "    Coordinate Bias:\n", "    X: {validation_metrics['coordinate_bias']['mean_dx_m']:.2f} ± {validation_metrics['coordinate_bias']['std_dx_m']:.2f}m\n", "    Y: {validation_metrics['coordinate_bias']['mean_dy_m']:.2f} ± {validation_metrics['coordinate_bias']['std_dy_m']:.2f}m\n", "    \"\"\"\n", "    axes[1, 2].text(0.1, 0.9, summary_text, transform=axes[1, 2].transAxes, \n", "                   fontsize=11, verticalalignment='top', fontfamily='monospace',\n", "                   bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    \n", "    if save_results:\n", "        plot_path = output_path / f\"{site_name}_metadata_validation_analysis.png\"\n", "        plt.savefig(plot_path, dpi=300, bbox_inches='tight')\n", "        print(f\"Saved validation plot: {plot_path}\")\n", "    \n", "    plt.show()\n", "else:\n", "    print(\"Visualization disabled\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Save Results\n", "\n", "Save validation results and metrics to files."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved matching results: ../../data/output_runs/validation/trino_enel_metadata_matching_results.csv\n", "Saved validation metrics: ../../data/output_runs/validation/trino_enel_metadata_validation_metrics.json\n", "Saved summary report: ../../data/output_runs/validation/trino_enel_metadata_validation_summary.txt\n", "\n", "All validation results saved to: ../../data/output_runs/validation\n", "\n", "Metadata Extraction Validation Complete!\n", "Final Match Rate: 75.6%\n", "Mean Spatial Error: 1.53m\n"]}], "source": ["if save_results:\n", "    # Save matching results CSV\n", "    matching_csv_path = output_path / f\"{site_name}_metadata_matching_results.csv\"\n", "    matching_results.to_csv(matching_csv_path, index=False)\n", "    print(f\"Saved matching results: {matching_csv_path}\")\n", "    \n", "    # Save validation metrics JSON\n", "    metrics_json_path = output_path / f\"{site_name}_metadata_validation_metrics.json\"\n", "    with open(metrics_json_path, 'w') as f:\n", "        json.dump(validation_metrics, f, indent=2)\n", "    print(f\"Saved validation metrics: {metrics_json_path}\")\n", "    \n", "    # Save summary report\n", "    summary_path = output_path / f\"{site_name}_metadata_validation_summary.txt\"\n", "    with open(summary_path, 'w') as f:\n", "        f.write(f\"Metadata Extraction Validation Summary\\n\")\n", "        f.write(f\"Site: {site_name}\\n\")\n", "        f.write(f\"Timestamp: {validation_metrics['timestamp']}\\n\")\n", "        f.write(f\"Tolerance: {validation_tolerance_m}m\\n\\n\")\n", "        \n", "        f.write(f\"Data Counts:\\n\")\n", "        f.write(f\"  Ground truth piles: {validation_metrics['ground_truth_count']}\\n\")\n", "        f.write(f\"  IFC metadata piles: {validation_metrics['ifc_metadata_count']}\\n\")\n", "        f.write(f\"  Matched pairs: {validation_metrics['matched_pairs']}\\n\")\n", "        f.write(f\"  Matches within tolerance: {validation_metrics['matches_within_tolerance']}\\n\\n\")\n", "        \n", "        f.write(f\"Accuracy Metrics:\\n\")\n", "        f.write(f\"  Match rate: {validation_metrics['match_rate_percent']:.1f}%\\n\")\n", "        f.write(f\"  Mean distance: {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}m\\n\")\n", "        f.write(f\"  RMSE: {validation_metrics['spatial_accuracy']['rmse_m']:.2f}m\\n\")\n", "        f.write(f\"  95th percentile: {validation_metrics['spatial_accuracy']['percentile_95_m']:.2f}m\\n\")\n", "        f.write(f\"  Coordinate bias X: {validation_metrics['coordinate_bias']['mean_dx_m']:.2f}m\\n\")\n", "        f.write(f\"  Coordinate bias Y: {validation_metrics['coordinate_bias']['mean_dy_m']:.2f}m\\n\")\n", "    \n", "    print(f\"Saved summary report: {summary_path}\")\n", "    \n", "    print(f\"\\nAll validation results saved to: {output_path}\")\n", "else:\n", "    print(\"Save results disabled\")\n", "\n", "print(f\"\\nMetadata Extraction Validation Complete!\")\n", "print(f\"Final Match Rate: {validation_metrics['match_rate_percent']:.1f}%\")\n", "print(f\"Mean Spatial Error: {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}m\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}