# Parameters (Papermill)
site_name = "trino_enel"
validation_tolerance_m = 0.25  # Spatial matching tolerance in meters
output_dir = "../../data/output_runs/validation"
enable_visualization = True
save_results = True

# Imports
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from datetime import datetime
from sklearn.neighbors import NearestNeighbors
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

# Set up paths
base_path = Path("../../data")
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

print("Metadata Extraction Validation - Ready!")
print(f"- Site: {site_name}")
print(f"- Tolerance: {validation_tolerance_m}m")
print(f"- Output: {output_path}")

# Load ground truth pile data (from survey records)
ground_truth_path = base_path / "processed" / site_name / "validation" / "Trino_PIles.csv"

if not ground_truth_path.exists():
    raise FileNotFoundError(f"Ground truth data not found at: {ground_truth_path.resolve()}")

ground_truth = pd.read_csv(ground_truth_path)

print(f"\n Loaded ground truth data: {len(ground_truth)} pile records")
print(f" Columns available: {list(ground_truth.columns)}")

print("\nSample of ground truth data:")
display(ground_truth.head())

print("\nGround Truth Summary:")
print(f"- Total pile entries     : {len(ground_truth)}")
print(f"- Easting (X) range      : {ground_truth['Easting'].min():.2f} to {ground_truth['Easting'].max():.2f}")
print(f"- Northing (Y) range     : {ground_truth['Northing'].min():.2f} to {ground_truth['Northing'].max():.2f}")
print(f"- Unique Table Numbers   : {ground_truth['Table_no'].nunique()}")


# Load IFC metadata (extracted pile locations from the digital design model)
ifc_metadata_path = base_path / "processed" / site_name / "advanced_ifc_metadata" / "advanced_tracker_piles.csv"
# ifc_metadata_path = base_path / "processed" / site_name / "ifc_metadata" / "GRE.EEC.S.00.IT.P.14353.00.265_elements_detailed.csv"

if not ifc_metadata_path.exists():
    raise FileNotFoundError(f"IFC metadata file not found at: {ifc_metadata_path.resolve()}")

ifc_metadata = pd.read_csv(ifc_metadata_path)

print(f"\nIFC metadata loaded successfully. Total records: {len(ifc_metadata)}")
print(f"Available columns: {list(ifc_metadata.columns)}")

print("\nPreview of IFC metadata:")
display(ifc_metadata.head())

# Summarize key statistics
print("\nIFC Metadata Summary:")
print(f"- Number of piles       : {len(ifc_metadata)}")
print(f"- X (Easting) range     : {ifc_metadata['X'].min():.2f} to {ifc_metadata['X'].max():.2f}")
print(f"- Y (Northing) range    : {ifc_metadata['Y'].min():.2f} to {ifc_metadata['Y'].max():.2f}")
print(f"- Z (Elevation) range   : {ifc_metadata['Z'].min():.2f} to {ifc_metadata['Z'].max():.2f}")
print(f"- Unique pile tags      : {ifc_metadata['Tag'].nunique()}")


from scipy.spatial.distance import cdist

# Extract coordinate arrays from both sources
ground_truth_coords = ground_truth[['Easting', 'Northing']].values
ifc_coords = ifc_metadata[['X', 'Y']].values

# Confirm shapes and readiness
print("Coordinate arrays prepared for spatial comparison:")
print(f"- Ground truth coordinate shape : {ground_truth_coords.shape}")
print(f"- IFC metadata coordinate shape : {ifc_coords.shape}")

# Compute pairwise Euclidean distances between each IFC pile and all ground truth piles
distances = cdist(ifc_coords, ground_truth_coords, metric='euclidean')
print(f"\nComputed distance matrix of shape: {distances.shape} (IFC x Ground Truth)")

# For each IFC pile, identify the nearest ground truth pile (minimum distance)
nearest_indices = np.argmin(distances, axis=1)
nearest_distances = np.min(distances, axis=1)

print("\nSummary of Nearest Neighbor Distances (in meters):")
print(f"- Mean distance        : {nearest_distances.mean():.2f}")
print(f"- Median distance      : {np.median(nearest_distances):.2f}")
print(f"- Minimum distance     : {nearest_distances.min():.2f}")
print(f"- Maximum distance     : {nearest_distances.max():.2f}")
print(f"- Standard deviation   : {nearest_distances.std():.2f}")


# Set decimal precision for coordinate rounding (e.g., ~1 cm precision)
precision = 4

matching_results = pd.DataFrame({
    'ifc_index': range(len(ifc_metadata)),
    'ifc_tag': ifc_metadata['Tag'].values,
    'ifc_x': ifc_metadata['X'].values,
    'ifc_y': ifc_metadata['Y'].values,
    'ifc_z': ifc_metadata['Z'].values,
    'gt_index': nearest_indices,
    'gt_s_no': ground_truth.iloc[nearest_indices]['S.No'].values,
    'gt_table_no': ground_truth.iloc[nearest_indices]['Table_no'].values,
    'gt_easting': ground_truth.iloc[nearest_indices]['Easting'].values,
    'gt_northing': ground_truth.iloc[nearest_indices]['Northing'].values
})

# Round coordinates from both sources to the same precision
matching_results['ifc_x'] = matching_results['ifc_x'].round(precision)
matching_results['ifc_y'] = matching_results['ifc_y'].round(precision)
matching_results['gt_easting'] = matching_results['gt_easting'].round(precision)
matching_results['gt_northing'] = matching_results['gt_northing'].round(precision)


# Calculate coordinate differences
matching_results['dx'] = matching_results['ifc_x'] - matching_results['gt_easting']
matching_results['dy'] = matching_results['ifc_y'] - matching_results['gt_northing']

# Calculate Euclidean distance and flag matches within tolerance
matching_results['distance_m'] = np.hypot(matching_results['dx'], matching_results['dy'])
matching_results['within_tolerance'] = matching_results['distance_m'] <= validation_tolerance_m

matching_results.to_csv("trino_enel_metadata_matching_results_fixed.csv", index=False)

print(f"Total matched records       : {len(matching_results)}")
print(f"Matches within tolerance    : {matching_results['within_tolerance'].sum()}")
print(f"Match rate                  : {matching_results['within_tolerance'].mean() * 100:.1f}%")

print("\nSample of matching results:")
display(matching_results.head(10))


# Calculate summary metrics for validation
validation_metrics = {
    'timestamp': datetime.now().isoformat(),
    'site_name': site_name,
    'validation_tolerance_m': validation_tolerance_m,
    'ground_truth_count': len(ground_truth),
    'ifc_metadata_count': len(ifc_metadata),
    'matched_pairs': len(matching_results),
    'matches_within_tolerance': int(matching_results['within_tolerance'].sum()),
    'match_rate_percent': float(matching_results['within_tolerance'].mean() * 100),
    'spatial_accuracy': {
        'mean_distance_m': float(nearest_distances.mean()),
        'median_distance_m': float(np.median(nearest_distances)),
        'std_distance_m': float(nearest_distances.std()),
        'min_distance_m': float(nearest_distances.min()),
        'max_distance_m': float(nearest_distances.max()),
        'rmse_m': float(np.sqrt(np.mean(nearest_distances**2))),
        'percentile_95_m': float(np.percentile(nearest_distances, 95)),
        'percentile_99_m': float(np.percentile(nearest_distances, 99))
    },
    'coordinate_bias': {
        'mean_dx_m': float(matching_results['dx'].mean()),
        'mean_dy_m': float(matching_results['dy'].mean()),
        'std_dx_m': float(matching_results['dx'].std()),
        'std_dy_m': float(matching_results['dy'].std())
    }
}

# Print a clear summary
print("Validation Summary Report")
print("-" * 30)
print(f"Site Name                  : {validation_metrics['site_name']}")
print(f"Validation Tolerance       : {validation_metrics['validation_tolerance_m']} meters")
print(f"Total Ground Truth Piles   : {validation_metrics['ground_truth_count']}")
print(f"Total IFC Metadata Piles   : {validation_metrics['ifc_metadata_count']}")
print(f"Matched Pile Pairs         : {validation_metrics['matched_pairs']}")
print(f"Matches Within Tolerance   : {validation_metrics['matches_within_tolerance']}")
print(f"Match Rate                 : {validation_metrics['match_rate_percent']:.1f}%")

print("\nDistance Accuracy (meters):")
print(f"  Average distance         : {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}")
print(f"  Median distance          : {validation_metrics['spatial_accuracy']['median_distance_m']:.2f}")
print(f"  Minimum distance         : {validation_metrics['spatial_accuracy']['min_distance_m']:.2f}")
print(f"  Maximum distance         : {validation_metrics['spatial_accuracy']['max_distance_m']:.2f}")
print(f"  Standard deviation       : {validation_metrics['spatial_accuracy']['std_distance_m']:.2f}")
print(f"  Root Mean Square Error   : {validation_metrics['spatial_accuracy']['rmse_m']:.2f}")
print(f"  95th percentile distance : {validation_metrics['spatial_accuracy']['percentile_95_m']:.2f}")

print("\nAverage Directional Offset (meters):")
print(f"  X (East-West) bias       : {validation_metrics['coordinate_bias']['mean_dx_m']:.2f}")
print(f"  Y (North-South) bias     : {validation_metrics['coordinate_bias']['mean_dy_m']:.2f}")


# Apply consistent plotting style
plt.style.use('default')
sns.set_palette("husl")


# Plot distribution of distance between IFC and Ground Truth
plt.figure(figsize=(8, 5))
plt.hist(nearest_distances, bins=50, color='skyblue', edgecolor='black', alpha=0.7)
plt.axvline(validation_tolerance_m, color='red', linestyle='--', label=f'Tolerance = {validation_tolerance_m} m')
plt.axvline(nearest_distances.mean(), color='orange', linestyle='-', label=f'Mean = {nearest_distances.mean():.2f} m')

plt.title("Distance Between Model and Actual Piles")
plt.xlabel("Distance (meters)")
plt.ylabel("Number of Piles")
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()


import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# Parameters
error_threshold_m = 1.0
colors = {
    'gt_only': '#1f77b4',        # blue
    'ifc_only': '#ff7f0e',       # orange
    'matched': '#9370DB',        # purple
    'bbox': 'black'
}

# --- Compute categories for overlay ---
matched = matching_results[matching_results['within_tolerance']]
gt_all = ground_truth.set_index('S.No')
ifc_all = ifc_metadata.set_index('Tag')

# Create sets of matched IDs
matched_gt_ids = set(matched['gt_s_no'])
matched_ifc_ids = set(matched['ifc_tag'])

# Identify unmatched ground truth and IFC records
unmatched_gt = ground_truth[~ground_truth['S.No'].isin(matched_gt_ids)]
unmatched_ifc = ifc_metadata[~ifc_metadata['Tag'].isin(matched_ifc_ids)]

# Auto-detect high error region
high_error_points = matching_results[matching_results['distance_m'] > error_threshold_m]
if not high_error_points.empty:
    x1, x2 = np.percentile(high_error_points['gt_easting'], [1, 99])
    y1, y2 = np.percentile(high_error_points['gt_northing'], [1, 99])
else:
    x1 = x2 = y1 = y2 = None

# --- Plot Overlay with Mismatch Categories ---
fig, ax = plt.subplots(figsize=(10, 10))
ax.set_title("Overlay: Pile Position Matches and Mismatches", fontsize=14)

# Plot unmatched ground truth (blue)
ax.scatter(
    unmatched_gt['Easting'], unmatched_gt['Northing'],
    c=colors['gt_only'], s=15, label='Ground Truth Only', alpha=0.6
)

# Plot unmatched IFC (orange)
ax.scatter(
    unmatched_ifc['X'], unmatched_ifc['Y'],
    c=colors['ifc_only'], s=15, label='IFC Metadata Only', alpha=0.6
)

# Plot matched pairs (purple)
ax.scatter(
    matched['gt_easting'], matched['gt_northing'],
    c=colors['matched'], s=15, label='Matched (within tolerance)', alpha=0.7
)

ax.set_xlabel("Easting (m)")
ax.set_ylabel("Northing (m)")
ax.axis('equal')
ax.grid(True, alpha=0.3)
ax.legend(loc='upper right', frameon=True)
plt.tight_layout()
plt.show()


# Scatter plot showing direction of pile placement error
plt.figure(figsize=(7, 7))
plt.scatter(matching_results['dx'], matching_results['dy'], c=matching_results['distance_m'], cmap='viridis', alpha=0.7)
plt.axhline(0, color='red', linestyle='--', alpha=0.5)
plt.axvline(0, color='red', linestyle='--', alpha=0.5)

plt.title("Direction of Error (X vs Y Offsets)")
plt.xlabel("X Offset (meters)")
plt.ylabel("Y Offset (meters)")
plt.grid(True, alpha=0.3)
plt.colorbar(label="Distance Error (m)")
plt.show()


# Plot how match rate changes as tolerance increases
thresholds = np.linspace(0, 10, 100)
match_rates = [np.mean(nearest_distances <= t) * 100 for t in thresholds]

plt.figure(figsize=(8, 5))
plt.plot(thresholds, match_rates, color='green', linewidth=2)
plt.axvline(validation_tolerance_m, color='red', linestyle='--', label=f'Current Tolerance ({validation_tolerance_m} m)')

plt.title("Match Rate vs Tolerance Threshold")
plt.xlabel("Distance Threshold (meters)")
plt.ylabel("Match Rate (%)")
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()


print("Validation Summary")
print("=" * 50)
print(f"Ground Truth Piles     : {len(ground_truth)}")
print(f"IFC Metadata Piles     : {len(ifc_metadata)}")
print(f"Match Rate             : {validation_metrics['match_rate_percent']:.1f}%")
print(f"Mean Distance Error    : {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f} m")
print(f"95th Percentile Error  : {validation_metrics['spatial_accuracy']['percentile_95_m']:.2f} m")
print(f"X Offset (Mean ± Std)  : {validation_metrics['coordinate_bias']['mean_dx_m']:.2f} ± {validation_metrics['coordinate_bias']['std_dx_m']:.2f} m")
print(f"Y Offset (Mean ± Std)  : {validation_metrics['coordinate_bias']['mean_dy_m']:.2f} ± {validation_metrics['coordinate_bias']['std_dy_m']:.2f} m")


if save_results:
    # Save matching results CSV
    matching_csv_path = output_path / f"{site_name}_metadata_matching_results.csv"
    matching_results.to_csv(matching_csv_path, index=False)
    print(f"Saved matching results: {matching_csv_path}")
    
    # Save validation metrics JSON
    metrics_json_path = output_path / f"{site_name}_metadata_validation_metrics.json"
    with open(metrics_json_path, 'w') as f:
        json.dump(validation_metrics, f, indent=2)
    print(f"Saved validation metrics: {metrics_json_path}")
    
    # Save summary report
    summary_path = output_path / f"{site_name}_metadata_validation_summary.txt"
    with open(summary_path, 'w') as f:
        f.write(f"Metadata Extraction Validation Summary\n")
        f.write(f"Site: {site_name}\n")
        f.write(f"Timestamp: {validation_metrics['timestamp']}\n")
        f.write(f"Tolerance: {validation_tolerance_m}m\n\n")
        
        f.write(f"Data Counts:\n")
        f.write(f"  Ground truth piles: {validation_metrics['ground_truth_count']}\n")
        f.write(f"  IFC metadata piles: {validation_metrics['ifc_metadata_count']}\n")
        f.write(f"  Matched pairs: {validation_metrics['matched_pairs']}\n")
        f.write(f"  Matches within tolerance: {validation_metrics['matches_within_tolerance']}\n\n")
        
        f.write(f"Accuracy Metrics:\n")
        f.write(f"  Match rate: {validation_metrics['match_rate_percent']:.1f}%\n")
        f.write(f"  Mean distance: {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}m\n")
        f.write(f"  RMSE: {validation_metrics['spatial_accuracy']['rmse_m']:.2f}m\n")
        f.write(f"  95th percentile: {validation_metrics['spatial_accuracy']['percentile_95_m']:.2f}m\n")
        f.write(f"  Coordinate bias X: {validation_metrics['coordinate_bias']['mean_dx_m']:.2f}m\n")
        f.write(f"  Coordinate bias Y: {validation_metrics['coordinate_bias']['mean_dy_m']:.2f}m\n")
    
    print(f"Saved summary report: {summary_path}")
    
    print(f"\nAll validation results saved to: {output_path}")
else:
    print("Save results disabled")

print(f"\nMetadata Extraction Validation Complete!")
print(f"Final Match Rate: {validation_metrics['match_rate_percent']:.1f}%")
print(f"Mean Spatial Error: {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}m")