# Parameters (Papermill)
site_name = "trino_enel"
validation_tolerance_m = 2.0  # Spatial matching tolerance in meters
output_dir = "../../data/output_runs/validation"
enable_visualization = True
save_results = True

# Imports
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from datetime import datetime
from sklearn.neighbors import NearestNeighbors
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

# Set up paths
base_path = Path("../../data")
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

print("Metadata Extraction Validation - Ready!")
print(f"- Site: {site_name}")
print(f"- Tolerance: {validation_tolerance_m}m")
print(f"- Output: {output_path}")

# Load ground truth pile data
ground_truth_path = base_path / "processed" / site_name / "validation" / "Trino_PIles.csv"

if not ground_truth_path.exists():
    raise FileNotFoundError(f"Ground truth data not found: {ground_truth_path}")

ground_truth = pd.read_csv(ground_truth_path)
print(f"Loaded ground truth: {len(ground_truth)} pile records")
print(f"Columns: {list(ground_truth.columns)}")

# Display sample data
print("\nSample ground truth data:")
display(ground_truth.head())

# Basic statistics
print(f"\nGround Truth Statistics:")
print(f"   Total piles: {len(ground_truth)}")
print(f"   Easting range: {ground_truth['Easting'].min():.2f} - {ground_truth['Easting'].max():.2f}")
print(f"   Northing range: {ground_truth['Northing'].min():.2f} - {ground_truth['Northing'].max():.2f}")
print(f"   Unique table numbers: {ground_truth['Table_no'].nunique()}")

# Load IFC metadata
ifc_metadata_path = base_path / "processed" / site_name / "advanced_ifc_metadata" / "advanced_tracker_piles.csv"

if not ifc_metadata_path.exists():
    raise FileNotFoundError(f"IFC metadata not found: {ifc_metadata_path}")

ifc_metadata = pd.read_csv(ifc_metadata_path)
print(f"Loaded IFC metadata: {len(ifc_metadata)} pile records")
print(f"Columns: {list(ifc_metadata.columns)}")

# Display sample data
print("\nSample IFC metadata:")
display(ifc_metadata.head())

# Basic statistics
print(f"\nIFC Metadata Statistics:")
print(f"   Total piles: {len(ifc_metadata)}")
print(f"   X range: {ifc_metadata['X'].min():.2f} - {ifc_metadata['X'].max():.2f}")
print(f"   Y range: {ifc_metadata['Y'].min():.2f} - {ifc_metadata['Y'].max():.2f}")
print(f"   Z range: {ifc_metadata['Z'].min():.2f} - {ifc_metadata['Z'].max():.2f}")
print(f"   Unique tags: {ifc_metadata['Tag'].nunique()}")

# Prepare coordinate arrays for matching
ground_truth_coords = ground_truth[['Easting', 'Northing']].values
ifc_coords = ifc_metadata[['X', 'Y']].values

print(f"Ground truth coordinates: {ground_truth_coords.shape}")
print(f"IFC coordinates: {ifc_coords.shape}")

# Calculate pairwise distances
distances = cdist(ifc_coords, ground_truth_coords, metric='euclidean')
print(f"Distance matrix shape: {distances.shape}")

# Find nearest neighbors for each IFC pile
nearest_indices = np.argmin(distances, axis=1)
nearest_distances = np.min(distances, axis=1)

print(f"\nDistance Statistics:")
print(f"   Mean distance: {nearest_distances.mean():.2f}m")
print(f"   Median distance: {np.median(nearest_distances):.2f}m")
print(f"   Min distance: {nearest_distances.min():.2f}m")
print(f"   Max distance: {nearest_distances.max():.2f}m")
print(f"   Std deviation: {nearest_distances.std():.2f}m")

# Create matching results dataframe
matching_results = pd.DataFrame({
    'ifc_index': range(len(ifc_metadata)),
    'ifc_tag': ifc_metadata['Tag'].values,
    'ifc_x': ifc_metadata['X'].values,
    'ifc_y': ifc_metadata['Y'].values,
    'ifc_z': ifc_metadata['Z'].values,
    'gt_index': nearest_indices,
    'gt_s_no': ground_truth.iloc[nearest_indices]['S.No'].values,
    'gt_table_no': ground_truth.iloc[nearest_indices]['Table_no'].values,
    'gt_easting': ground_truth.iloc[nearest_indices]['Easting'].values,
    'gt_northing': ground_truth.iloc[nearest_indices]['Northing'].values,
    'distance_m': nearest_distances,
    'within_tolerance': nearest_distances <= validation_tolerance_m
})

# Calculate coordinate differences
matching_results['dx'] = matching_results['ifc_x'] - matching_results['gt_easting']
matching_results['dy'] = matching_results['ifc_y'] - matching_results['gt_northing']

print(f"Created matching results: {len(matching_results)} records")
print(f"Matches within tolerance ({validation_tolerance_m}m): {matching_results['within_tolerance'].sum()}")
print(f"Match rate: {matching_results['within_tolerance'].mean()*100:.1f}%")

# Display sample matching results
print("\nSample matching results:")
display(matching_results.head(10))

# Calculate validation metrics
validation_metrics = {
    'timestamp': datetime.now().isoformat(),
    'site_name': site_name,
    'validation_tolerance_m': validation_tolerance_m,
    'ground_truth_count': len(ground_truth),
    'ifc_metadata_count': len(ifc_metadata),
    'matched_pairs': len(matching_results),
    'matches_within_tolerance': int(matching_results['within_tolerance'].sum()),
    'match_rate_percent': float(matching_results['within_tolerance'].mean() * 100),
    'spatial_accuracy': {
        'mean_distance_m': float(nearest_distances.mean()),
        'median_distance_m': float(np.median(nearest_distances)),
        'std_distance_m': float(nearest_distances.std()),
        'min_distance_m': float(nearest_distances.min()),
        'max_distance_m': float(nearest_distances.max()),
        'rmse_m': float(np.sqrt(np.mean(nearest_distances**2))),
        'percentile_95_m': float(np.percentile(nearest_distances, 95)),
        'percentile_99_m': float(np.percentile(nearest_distances, 99))
    },
    'coordinate_bias': {
        'mean_dx_m': float(matching_results['dx'].mean()),
        'mean_dy_m': float(matching_results['dy'].mean()),
        'std_dx_m': float(matching_results['dx'].std()),
        'std_dy_m': float(matching_results['dy'].std())
    }
}

print("Validation Metrics Summary:")
print(f"   Ground truth piles: {validation_metrics['ground_truth_count']}")
print(f"   IFC metadata piles: {validation_metrics['ifc_metadata_count']}")
print(f"   Match rate: {validation_metrics['match_rate_percent']:.1f}%")
print(f"   Mean spatial error: {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}m")
print(f"   RMSE: {validation_metrics['spatial_accuracy']['rmse_m']:.2f}m")
print(f"   95th percentile error: {validation_metrics['spatial_accuracy']['percentile_95_m']:.2f}m")
print(f"   Coordinate bias (X): {validation_metrics['coordinate_bias']['mean_dx_m']:.2f}m")
print(f"   Coordinate bias (Y): {validation_metrics['coordinate_bias']['mean_dy_m']:.2f}m")

if enable_visualization:
    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Create a comprehensive validation plot
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'Metadata Extraction Validation - {site_name.upper()}', fontsize=16, fontweight='bold')
    
    # 1. Distance distribution
    axes[0, 0].hist(nearest_distances, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].axvline(validation_tolerance_m, color='red', linestyle='--', label=f'Tolerance ({validation_tolerance_m}m)')
    axes[0, 0].axvline(nearest_distances.mean(), color='orange', linestyle='-', label=f'Mean ({nearest_distances.mean():.2f}m)')
    axes[0, 0].set_xlabel('Distance (m)')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_title('Distance Distribution')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Coordinate scatter plot
    scatter = axes[0, 1].scatter(ground_truth['Easting'], ground_truth['Northing'], 
                                c='blue', alpha=0.6, s=20, label='Ground Truth')
    axes[0, 1].scatter(ifc_metadata['X'], ifc_metadata['Y'], 
                      c='red', alpha=0.6, s=20, label='IFC Metadata')
    axes[0, 1].set_xlabel('Easting (m)')
    axes[0, 1].set_ylabel('Northing (m)')
    axes[0, 1].set_title('Spatial Distribution Comparison')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].axis('equal')
    
    # 3. Coordinate bias analysis
    axes[0, 2].scatter(matching_results['dx'], matching_results['dy'], 
                      c=matching_results['distance_m'], cmap='viridis', alpha=0.7)
    axes[0, 2].axhline(0, color='red', linestyle='--', alpha=0.5)
    axes[0, 2].axvline(0, color='red', linestyle='--', alpha=0.5)
    axes[0, 2].set_xlabel('X Bias (m)')
    axes[0, 2].set_ylabel('Y Bias (m)')
    axes[0, 2].set_title('Coordinate Bias Analysis')
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. Match rate by distance threshold
    thresholds = np.linspace(0, 10, 100)
    match_rates = [np.mean(nearest_distances <= t) * 100 for t in thresholds]
    axes[1, 0].plot(thresholds, match_rates, linewidth=2, color='green')
    axes[1, 0].axvline(validation_tolerance_m, color='red', linestyle='--', 
                      label=f'Current tolerance ({validation_tolerance_m}m)')
    axes[1, 0].set_xlabel('Distance Threshold (m)')
    axes[1, 0].set_ylabel('Match Rate (%)')
    axes[1, 0].set_title('Match Rate vs Distance Threshold')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. Distance vs IFC tag (sample)
    sample_size = min(100, len(matching_results))
    sample_data = matching_results.sample(sample_size)
    axes[1, 1].scatter(range(len(sample_data)), sample_data['distance_m'], alpha=0.7)
    axes[1, 1].axhline(validation_tolerance_m, color='red', linestyle='--', alpha=0.7)
    axes[1, 1].set_xlabel('Sample Index')
    axes[1, 1].set_ylabel('Distance (m)')
    axes[1, 1].set_title(f'Distance Distribution (Sample of {sample_size})')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. Summary statistics
    axes[1, 2].axis('off')
    summary_text = f"""
    VALIDATION SUMMARY
    
    Ground Truth Piles: {len(ground_truth):,}
    IFC Metadata Piles: {len(ifc_metadata):,}
    
    Match Rate: {validation_metrics['match_rate_percent']:.1f}%
    Mean Error: {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}m
    RMSE: {validation_metrics['spatial_accuracy']['rmse_m']:.2f}m
    95th Percentile: {validation_metrics['spatial_accuracy']['percentile_95_m']:.2f}m
    
    Coordinate Bias:
    X: {validation_metrics['coordinate_bias']['mean_dx_m']:.2f} ± {validation_metrics['coordinate_bias']['std_dx_m']:.2f}m
    Y: {validation_metrics['coordinate_bias']['mean_dy_m']:.2f} ± {validation_metrics['coordinate_bias']['std_dy_m']:.2f}m
    """
    axes[1, 2].text(0.1, 0.9, summary_text, transform=axes[1, 2].transAxes, 
                   fontsize=11, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    
    if save_results:
        plot_path = output_path / f"{site_name}_metadata_validation_analysis.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f"Saved validation plot: {plot_path}")
    
    plt.show()
else:
    print("Visualization disabled")

if save_results:
    # Save matching results CSV
    matching_csv_path = output_path / f"{site_name}_metadata_matching_results.csv"
    matching_results.to_csv(matching_csv_path, index=False)
    print(f"Saved matching results: {matching_csv_path}")
    
    # Save validation metrics JSON
    metrics_json_path = output_path / f"{site_name}_metadata_validation_metrics.json"
    with open(metrics_json_path, 'w') as f:
        json.dump(validation_metrics, f, indent=2)
    print(f"Saved validation metrics: {metrics_json_path}")
    
    # Save summary report
    summary_path = output_path / f"{site_name}_metadata_validation_summary.txt"
    with open(summary_path, 'w') as f:
        f.write(f"Metadata Extraction Validation Summary\n")
        f.write(f"Site: {site_name}\n")
        f.write(f"Timestamp: {validation_metrics['timestamp']}\n")
        f.write(f"Tolerance: {validation_tolerance_m}m\n\n")
        
        f.write(f"Data Counts:\n")
        f.write(f"  Ground truth piles: {validation_metrics['ground_truth_count']}\n")
        f.write(f"  IFC metadata piles: {validation_metrics['ifc_metadata_count']}\n")
        f.write(f"  Matched pairs: {validation_metrics['matched_pairs']}\n")
        f.write(f"  Matches within tolerance: {validation_metrics['matches_within_tolerance']}\n\n")
        
        f.write(f"Accuracy Metrics:\n")
        f.write(f"  Match rate: {validation_metrics['match_rate_percent']:.1f}%\n")
        f.write(f"  Mean distance: {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}m\n")
        f.write(f"  RMSE: {validation_metrics['spatial_accuracy']['rmse_m']:.2f}m\n")
        f.write(f"  95th percentile: {validation_metrics['spatial_accuracy']['percentile_95_m']:.2f}m\n")
        f.write(f"  Coordinate bias X: {validation_metrics['coordinate_bias']['mean_dx_m']:.2f}m\n")
        f.write(f"  Coordinate bias Y: {validation_metrics['coordinate_bias']['mean_dy_m']:.2f}m\n")
    
    print(f"Saved summary report: {summary_path}")
    
    print(f"\nAll validation results saved to: {output_path}")
else:
    print("Save results disabled")

print(f"\nMetadata Extraction Validation Complete!")
print(f"Final Match Rate: {validation_metrics['match_rate_percent']:.1f}%")
print(f"Mean Spatial Error: {validation_metrics['spatial_accuracy']['mean_distance_m']:.2f}m")