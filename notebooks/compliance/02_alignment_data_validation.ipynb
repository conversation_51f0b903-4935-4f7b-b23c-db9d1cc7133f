# Parameters (Papermill)
site_name = "trino_enel"
ground_method = "csf"  # Ground segmentation method used
alignment_tolerance_m = 0.25  # Alignment quality tolerance in meters
output_dir = "../../data/output_runs/validation"
enable_visualization = True
save_results = True

# Imports
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from datetime import datetime
import open3d as o3d
from sklearn.neighbors import NearestNeighbors
from scipy.spatial.distance import cdist
from scipy.spatial.transform import Rotation
import warnings
warnings.filterwarnings('ignore')

# Set up paths
base_path = Path("../../data")
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

print("Alignment Data Validation - Ready!")
print(f"- Site: {site_name}")
print(f"- Ground method: {ground_method}")
print(f"- Tolerance: {alignment_tolerance_m}m")
print(f"- Output: {output_path}")

# Load ground truth pile data
ground_truth_path = base_path / "processed" / site_name / "validation" / "Trino_PIles.csv"
ground_truth = pd.read_csv(ground_truth_path)
print(f"Loaded ground truth: {len(ground_truth)} pile records")

# Load IFC metadata
ifc_metadata_path = base_path / "processed" / site_name / "advanced_ifc_metadata" / "advanced_tracker_piles.csv"
ifc_metadata = pd.read_csv(ifc_metadata_path)
print(f"Loaded IFC metadata: {len(ifc_metadata)} pile records")

# Display coordinate ranges
print(f"\nCoordinate Ranges:")
print(f"   Ground Truth - Easting: {ground_truth['Easting'].min():.2f} to {ground_truth['Easting'].max():.2f}")
print(f"   Ground Truth - Northing: {ground_truth['Northing'].min():.2f} to {ground_truth['Northing'].max():.2f}")
print(f"   IFC - X: {ifc_metadata['X'].min():.2f} to {ifc_metadata['X'].max():.2f}")
print(f"   IFC - Y: {ifc_metadata['Y'].min():.2f} to {ifc_metadata['Y'].max():.2f}")
print(f"   IFC - Z: {ifc_metadata['Z'].min():.2f} to {ifc_metadata['Z'].max():.2f}")

# Load the aligned 3D model of piles
aligned_pc_path = base_path / "output_runs" / "icp_alignment_corrected" / ground_method / f"aligned_ifc_{ground_method}.ply"

aligned_point_cloud = None

if aligned_pc_path.exists():
    # Read the aligned point cloud (3D coordinates after alignment)
    aligned_point_cloud = o3d.io.read_point_cloud(str(aligned_pc_path))
    print(f"Loaded aligned 3D point cloud with {len(aligned_point_cloud.points)} points.")

    # Show the overall spread of points in space
    points = np.asarray(aligned_point_cloud.points)
    print("Point Cloud Spatial Range:")
    print(f"   X direction: {points[:, 0].min():.2f} to {points[:, 0].max():.2f} meters")
    print(f"   Y direction: {points[:, 1].min():.2f} to {points[:, 1].max():.2f} meters")
    print(f"   Z direction (height): {points[:, 2].min():.2f} to {points[:, 2].max():.2f} meters")

else:
    # File was not found — skip or simulate for testing
    print(f"Aligned 3D point cloud not found at:\n   {aligned_pc_path}")
    print("Using fallback or test data for alignment validation...")

# Load the transformation details (if available)
transform_metadata_path = base_path / "output_runs" / "icp_alignment_corrected" / ground_method / "transformation_metadata.json"
transform_metadata = None

if transform_metadata_path.exists():
    with open(transform_metadata_path, 'r') as f:
        transform_metadata = json.load(f)
    print("Transformation data loaded.")
    print("Summary of applied transformation:")
    print(transform_metadata.get('summary', 'No transformation summary available.'))
else:
    print(f"No transformation metadata found at:\n   {transform_metadata_path}")


# STEP 1: Prepare coordinate data from both sources
# - 'Ground truth' = actual pile locations from field survey
# - 'IFC' = pile positions from the design model

ground_truth_coords = ground_truth[['Easting', 'Northing']].values
ifc_coords = ifc_metadata[['X', 'Y']].values

# STEP 2: Compare each IFC pile to the nearest actual (ground truth) pile
# This tells us how closely the model aligns with the real-world positions
from scipy.spatial.distance import cdist
distances = cdist(ifc_coords, ground_truth_coords, metric='euclidean')
nearest_indices = np.argmin(distances, axis=1)      # index of closest ground truth pile
nearest_distances = np.min(distances, axis=1)       # distance to that closest pile

# Report how far off the IFC model is before any alignment fix
print("Alignment Error (Before Any Correction):")
print(f"   Average mismatch: {nearest_distances.mean():.2f} meters")
print(f"   Median mismatch : {np.median(nearest_distances):.2f} meters")
print(f"   Worst mismatch  : {nearest_distances.max():.2f} meters")
print(f"   Overall RMSE    : {np.sqrt(np.mean(nearest_distances**2)):.2f} meters")

# STEP 3: Build a validation table showing matched pairs and differences
alignment_validation = pd.DataFrame({
    'ifc_tag': ifc_metadata['Tag'].values,
    'ifc_x': ifc_metadata['X'].values,
    'ifc_y': ifc_metadata['Y'].values,
    'ifc_z': ifc_metadata['Z'].values,
    'gt_easting': ground_truth.iloc[nearest_indices]['Easting'].values,
    'gt_northing': ground_truth.iloc[nearest_indices]['Northing'].values,
    'gt_table_no': ground_truth.iloc[nearest_indices]['Table_no'].values,
    'distance_m': nearest_distances,
    'within_tolerance': nearest_distances <= alignment_tolerance_m  # Whether match is acceptable
})

# STEP 4: Calculate direction-wise offsets (error in X and Y)
alignment_validation['dx'] = alignment_validation['ifc_x'] - alignment_validation['gt_easting']
alignment_validation['dy'] = alignment_validation['ifc_y'] - alignment_validation['gt_northing']

# STEP 5: Print quality summary
print("\nAlignment Validation Summary:")
print(f"   Total pile comparisons: {len(alignment_validation)}")
print(f"   Piles within tolerance ({alignment_tolerance_m}m): {alignment_validation['within_tolerance'].sum()}")
print(f"   Alignment Success Rate: {alignment_validation['within_tolerance'].mean() * 100:.1f}%")
print(f"   Average X deviation    : {alignment_validation['dx'].mean():.2f} ± {alignment_validation['dx'].std():.2f} meters")
print(f"   Average Y deviation    : {alignment_validation['dy'].mean():.2f} ± {alignment_validation['dy'].std():.2f} meters")

# STEP 6: Show a few examples for quick inspection
print("\nSample matched pile results:")
display(alignment_validation.head(10))


# Compute summary statistics for alignment validation
# This helps quantify how well the IFC data aligns with the actual pile layout

alignment_metrics = {
    'timestamp': datetime.now().isoformat(),
    'site_name': site_name,
    'ground_method': ground_method,
    'alignment_tolerance_m': alignment_tolerance_m,

    # Count of input data
    'data_counts': {
        'ground_truth_piles': len(ground_truth),                  # Actual surveyed piles
        'ifc_metadata_piles': len(ifc_metadata),                  # Piles in the design model
        'aligned_pairs': len(alignment_validation),              # Total matched pairs considered
        'successful_alignments': int(alignment_validation['within_tolerance'].sum())  # Matches within tolerance
    },

    # Quality of spatial alignment (how far off the points are)
    'alignment_quality': {
        'success_rate_percent': float(alignment_validation['within_tolerance'].mean() * 100),
        'mean_distance_m': float(alignment_validation['distance_m'].mean()),          # Average error
        'median_distance_m': float(alignment_validation['distance_m'].median()),      # Middle value of errors
        'rmse_m': float(np.sqrt(np.mean(alignment_validation['distance_m'] ** 2))),   # Root Mean Square Error
        'std_distance_m': float(alignment_validation['distance_m'].std()),            # Spread of errors
        'max_distance_m': float(alignment_validation['distance_m'].max()),            # Worst case error
        'percentile_95_m': float(np.percentile(alignment_validation['distance_m'], 95)),
        'percentile_99_m': float(np.percentile(alignment_validation['distance_m'], 99))
    },

    # Bias in direction (systematic offset between IFC and ground truth)
    'coordinate_bias': {
        'mean_dx_m': float(alignment_validation['dx'].mean()),         # Average east-west offset
        'mean_dy_m': float(alignment_validation['dy'].mean()),         # Average north-south offset
        'std_dx_m': float(alignment_validation['dx'].std()),           # Variation in X offset
        'std_dy_m': float(alignment_validation['dy'].std()),           # Variation in Y offset
        'systematic_bias_magnitude_m': float(np.sqrt(
            alignment_validation['dx'].mean() ** 2 + alignment_validation['dy'].mean() ** 2))  # Overall directional drift
    }
}

# Include transformation info if available (e.g. rotation, shift, scale)
if transform_metadata:
    alignment_metrics['transformation'] = transform_metadata

# Print key highlights for users reviewing the alignment results
print("Alignment Quality Summary:")
print(f"   Alignment success rate: {alignment_metrics['alignment_quality']['success_rate_percent']:.1f}%")
print(f"   Average error between design and actual: {alignment_metrics['alignment_quality']['mean_distance_m']:.2f} meters")
print(f"   Root Mean Square Error (RMSE): {alignment_metrics['alignment_quality']['rmse_m']:.2f} meters")
print(f"   95th percentile error (most errors under): {alignment_metrics['alignment_quality']['percentile_95_m']:.2f} meters")
print(f"   Overall directional bias (systematic drift): {alignment_metrics['coordinate_bias']['systematic_bias_magnitude_m']:.2f} meters")


# Apply consistent plotting style
plt.style.use('default')
sns.set_palette("husl")


# Plot 1: Histogram of alignment errors
plt.figure(figsize=(6, 5))
plt.hist(alignment_validation['distance_m'], bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
plt.axvline(alignment_tolerance_m, color='red', linestyle='--', label=f'Tolerance = {alignment_tolerance_m} m')
plt.axvline(alignment_validation['distance_m'].mean(), color='orange', linestyle='-', label=f'Mean = {alignment_validation["distance_m"].mean():.2f} m')
plt.xlabel('Alignment Error (meters)')
plt.ylabel('Number of Piles')
plt.title('Distribution of Alignment Errors')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()


# Plot 2: Side-by-side comparison of coordinates
plt.figure(figsize=(6, 5))
plt.scatter(ground_truth['Easting'], ground_truth['Northing'], label='Ground Truth', c='blue', alpha=0.6, s=20)
plt.scatter(ifc_metadata['X'], ifc_metadata['Y'], label='IFC Aligned', c='red', alpha=0.6, s=20)
plt.xlabel('Easting (m)')
plt.ylabel('Northing (m)')
plt.title('Coordinate Comparison: Ground Truth vs IFC')
plt.legend()
plt.axis('equal')
plt.grid(True, alpha=0.3)
plt.show()


# Plot 3: Bias vectors showing direction and magnitude of misalignment
sample_size = min(200, len(alignment_validation))
sample_data = alignment_validation.sample(sample_size)

plt.figure(figsize=(6, 5))
quiver = plt.quiver(
    sample_data['gt_easting'], sample_data['gt_northing'],
    sample_data['dx'], sample_data['dy'],
    sample_data['distance_m'], cmap='viridis', alpha=0.7
)
plt.xlabel('Easting (m)')
plt.ylabel('Northing (m)')
plt.title(f'Alignment Direction Arrows (Sample of {sample_size})')
plt.colorbar(quiver, label='Distance (m)')
plt.grid(True, alpha=0.3)
plt.axis('equal')
plt.show()


# Plot 4: How alignment success rate improves with larger tolerances
thresholds = np.linspace(0, 10, 100)
success_rates = [np.mean(alignment_validation['distance_m'] <= t) * 100 for t in thresholds]

plt.figure(figsize=(6, 5))
plt.plot(thresholds, success_rates, color='green', linewidth=2)
plt.axvline(alignment_tolerance_m, color='red', linestyle='--', label=f'Tolerance = {alignment_tolerance_m} m')
plt.xlabel('Distance Threshold (m)')
plt.ylabel('Success Rate (%)')
plt.title('Success Rate vs Tolerance')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()


# Plot 5: Shows how the IFC points are misaligned in X/Y direction
plt.figure(figsize=(6, 5))
sc = plt.scatter(alignment_validation['dx'], alignment_validation['dy'], 
                 c=alignment_validation['distance_m'], cmap='plasma', alpha=0.7)
plt.axhline(0, color='red', linestyle='--', alpha=0.5)
plt.axvline(0, color='red', linestyle='--', alpha=0.5)
plt.xlabel('X Offset (m)')
plt.ylabel('Y Offset (m)')
plt.title('Bias Between Ground Truth and IFC')
plt.colorbar(sc, label='Distance (m)')
plt.grid(True, alpha=0.3)
plt.axis('equal')
plt.show()


# Print alignment validation summary as plain text
summary_text = f"""
Alignment Summary Report
-------------------------

Ground Method           : {ground_method.upper()}
Total Ground Truth Piles: {len(ground_truth)}
Total IFC Piles         : {len(ifc_metadata)}

Success Rate (%)        : {alignment_metrics['alignment_quality']['success_rate_percent']:.1f}
Mean Alignment Error (m): {alignment_metrics['alignment_quality']['mean_distance_m']:.2f}
RMSE (m)                : {alignment_metrics['alignment_quality']['rmse_m']:.2f}
95th Percentile Error   : {alignment_metrics['alignment_quality']['percentile_95_m']:.2f}

Bias in X (m)           : {alignment_metrics['coordinate_bias']['mean_dx_m']:.2f} ± {alignment_metrics['coordinate_bias']['std_dx_m']:.2f}
Bias in Y (m)           : {alignment_metrics['coordinate_bias']['mean_dy_m']:.2f} ± {alignment_metrics['coordinate_bias']['std_dy_m']:.2f}
Overall Systematic Drift: {alignment_metrics['coordinate_bias']['systematic_bias_magnitude_m']:.2f} m
"""

print(summary_text)


if save_results:
    # Save alignment validation results CSV
    alignment_csv_path = output_path / f"{site_name}_{ground_method}_alignment_validation_results.csv"
    alignment_validation.to_csv(alignment_csv_path, index=False)
    print(f"Saved alignment validation results: {alignment_csv_path}")
    
    # Save alignment metrics JSON
    metrics_json_path = output_path / f"{site_name}_{ground_method}_alignment_validation_metrics.json"
    with open(metrics_json_path, 'w') as f:
        json.dump(alignment_metrics, f, indent=2)
    print(f"Saved alignment metrics: {metrics_json_path}")
    
    # Save detailed summary report
    summary_path = output_path / f"{site_name}_{ground_method}_alignment_validation_summary.txt"
    with open(summary_path, 'w') as f:
        f.write(f"Alignment Data Validation Summary\n")
        f.write(f"Site: {site_name}\n")
        f.write(f"Ground Method: {ground_method}\n")
        f.write(f"Timestamp: {alignment_metrics['timestamp']}\n")
        f.write(f"Tolerance: {alignment_tolerance_m}m\n\n")
        
        f.write(f"Data Counts:\n")
        f.write(f"  Ground truth piles: {alignment_metrics['data_counts']['ground_truth_piles']}\n")
        f.write(f"  IFC metadata piles: {alignment_metrics['data_counts']['ifc_metadata_piles']}\n")
        f.write(f"  Aligned pairs: {alignment_metrics['data_counts']['aligned_pairs']}\n")
        f.write(f"  Successful alignments: {alignment_metrics['data_counts']['successful_alignments']}\n\n")
        
        f.write(f"Alignment Quality:\n")
        f.write(f"  Success rate: {alignment_metrics['alignment_quality']['success_rate_percent']:.1f}%\n")
        f.write(f"  Mean error: {alignment_metrics['alignment_quality']['mean_distance_m']:.2f}m\n")
        f.write(f"  RMSE: {alignment_metrics['alignment_quality']['rmse_m']:.2f}m\n")
        f.write(f"  95th percentile: {alignment_metrics['alignment_quality']['percentile_95_m']:.2f}m\n")
        f.write(f"  Max error: {alignment_metrics['alignment_quality']['max_distance_m']:.2f}m\n\n")
        
        f.write(f"Coordinate Bias:\n")
        f.write(f"  Mean X bias: {alignment_metrics['coordinate_bias']['mean_dx_m']:.2f}m\n")
        f.write(f"  Mean Y bias: {alignment_metrics['coordinate_bias']['mean_dy_m']:.2f}m\n")
        f.write(f"  X bias std: {alignment_metrics['coordinate_bias']['std_dx_m']:.2f}m\n")
        f.write(f"  Y bias std: {alignment_metrics['coordinate_bias']['std_dy_m']:.2f}m\n")
        f.write(f"  Systematic bias magnitude: {alignment_metrics['coordinate_bias']['systematic_bias_magnitude_m']:.2f}m\n")
        
        if 'transformation' in alignment_metrics:
            f.write(f"\nTransformation Details:\n")
            f.write(f"  {json.dumps(alignment_metrics['transformation'], indent=2)}\n")
    
    print(f"Saved summary report: {summary_path}")
    
    print(f"\nAll alignment validation results saved to: {output_path}")
else:
    print("Save results disabled")

print(f"\nAlignment Data Validation Complete!")
print(f" Final Success Rate: {alignment_metrics['alignment_quality']['success_rate_percent']:.1f}%")
print(f" Mean Alignment Error: {alignment_metrics['alignment_quality']['mean_distance_m']:.2f}m")
print(f" Ground Method: {ground_method.upper()}")