{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# GCP vs Grading Analysis\n",
    "\n",
    "This notebook performs a comprehensive statistical analysis to distinguish between Ground Control Point (GCP) deficiency and site grading effects as the cause of systematic coordinate bias.\n",
    "\n",
    "**Purpose**: Determine whether the observed -0.69m X-bias is due to GCP deficiency or site grading/slope effects  \n",
    "**Input**: Validation results from metadata extraction analysis  \n",
    "**Output**: Statistical diagnostic analysis with definitive conclusion  \n",
    "\n",
    "**Author**: Preetam Balijepalli  \n",
    "**Date**: July 2025  \n",
    "**Project**: As-Built Foundation Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {
    "tags": [\n",
    "     \"parameters\"\n",
    "    ]\n",
   },
   "outputs": [],
   "source": [
    "# Parameters (Papermill)\n",
    "site_name = \"trino_enel\"\n",
    "validation_results_file = \"../../data/output_runs/validation/trino_enel_metadata_matching_results.csv\"\n",
    "output_dir = \"../../data/output_runs/validation\"\n",
    "enable_visualization = True\n",
    "save_results = True"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Imports\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from pathlib import Path\n",
    "import json\n",
    "from datetime import datetime\n",
    "from scipy.stats import pearsonr\n",
    "from scipy import stats\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set up paths\n",
    "output_path = Path(output_dir)\n",
    "output_path.mkdir(parents=True, exist_ok=True)\n",
    "\n",
    "print(\"GCP vs Grading Analysis - Ready!\")\n",
    "print(f\"Site: {site_name}\")\n",
    "print(f\"Output: {output_path}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Load Validation Data\n",
    "\n",
    "Load the metadata validation results containing coordinate bias information."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load validation results\n",
    "validation_results = pd.read_csv(validation_results_file)\n",
    "\n",
    "print(f\"Loaded validation results: {len(validation_results)} records\")\n",
    "print(f\"Columns: {list(validation_results.columns)}\")\n",
    "\n",
    "# Display basic statistics\n",
    "print(\"\\nBasic Bias Statistics:\")\n",
    "print(f\"  Mean X-bias (dx): {validation_results['dx'].mean():.3f}m\")\n",
    "print(f\"  Mean Y-bias (dy): {validation_results['dy'].mean():.3f}m\")\n",
    "print(f\"  Std X-bias: {validation_results['dx'].std():.3f}m\")\n",
    "print(f\"  Std Y-bias: {validation_results['dy'].std():.3f}m\")\n",
    "print(f\"  Mean distance error: {validation_results['distance_m'].mean():.3f}m\")\n",
    "\n",
    "# Calculate systematic bias magnitude and direction\n",
    "dx_mean = validation_results['dx'].mean()\n",
    "dy_mean = validation_results['dy'].mean()\n",
    "bias_magnitude = np.sqrt(dx_mean**2 + dy_mean**2)\n",
    "bias_direction = np.degrees(np.arctan2(dy_mean, dx_mean))\n",
    "\n",
    "print(f\"\\nSystematic Bias Analysis:\")\n",
    "print(f\"  Bias magnitude: {bias_magnitude:.3f}m\")\n",
    "print(f\"  Bias direction: {bias_direction:.1f} degrees from East\")\n",
    "\n",
    "# Display sample data\n",
    "print(\"\\nSample validation data:\")\n",
    "display(validation_results[['ifc_x', 'ifc_y', 'gt_easting', 'gt_northing', 'dx', 'dy', 'distance_m']].head())"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. GCP Deficiency Diagnostic Tests\n",
    "\n",
    "Perform statistical tests to identify signatures of GCP deficiency."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Calculate site center for radial analysis\n",
    "site_center_x = validation_results['ifc_x'].mean()\n",
    "site_center_y = validation_results['ifc_y'].mean()\n",
    "\n",
    "print(f\"Site center coordinates: ({site_center_x:.2f}, {site_center_y:.2f})\")\n",
    "\n",
    "# Calculate distance from center for each point\n",
    "validation_results_copy = validation_results.copy()\n",
    "validation_results_copy['dist_from_center'] = np.sqrt(\n",
    "    (validation_results_copy['ifc_x'] - site_center_x)**2 + \n",
    "    (validation_results_copy['ifc_y'] - site_center_y)**2\n",
    ")\n",
    "\n",
    "print(f\"Distance from center range: {validation_results_copy['dist_from_center'].min():.1f} - {validation_results_copy['dist_from_center'].max():.1f}m\")\n",
    "\n",
    "# Test 1: Radial error pattern (GCP signature)\n",
    "center_dx_corr, center_dx_p = pearsonr(validation_results_copy['dist_from_center'], validation_results_copy['dx'])\n",
    "center_dy_corr, center_dy_p = pearsonr(validation_results_copy['dist_from_center'], validation_results_copy['dy'])\n",
    "center_dist_corr, center_dist_p = pearsonr(validation_results_copy['dist_from_center'], validation_results_copy['distance_m'])\n",
    "\n",
    "print(\"\\nTest 1: Radial Error Pattern Analysis\")\n",
    "print(\"(GCP deficiency typically shows increasing errors with distance from center)\")\n",
    "print(f\"  Distance-from-center vs X-bias correlation: r = {center_dx_corr:.3f} (p = {center_dx_p:.6f})\")\n",
    "print(f\"  Distance-from-center vs Y-bias correlation: r = {center_dy_corr:.3f} (p = {center_dy_p:.6f})\")\n",
    "print(f\"  Distance-from-center vs total-error correlation: r = {center_dist_corr:.3f} (p = {center_dist_p:.6f})\")\n",
    "\n",
    "# Interpretation\n",
    "if abs(center_dist_corr) > 0.3:\n",
    "    radial_interpretation = \"STRONG radial pattern - SUGGESTS GCP deficiency\"\n",
    "    radial_score = 3\n",
    "elif abs(center_dist_corr) > 0.15:\n",
    "    radial_interpretation = \"MODERATE radial pattern - POSSIBLE GCP issue\"\n",
    "    radial_score = 2\n",
    "elif abs(center_dist_corr) > 0.05:\n",
    "    radial_interpretation = \"WEAK radial pattern - MINIMAL GCP signature\"\n",
    "    radial_score = 1\n",
    "else:\n",
    "    radial_interpretation = \"NO radial pattern - UNLIKELY GCP issue\"\n",
    "    radial_score = 0\n",
    "\n",
    "print(f\"  Interpretation: {radial_interpretation}\")\n",
    "print(f\"  GCP Score from radial test: {radial_score}/3\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test 2: Edge vs Center Error Analysis\n",
    "x_range = validation_results['ifc_x'].max() - validation_results['ifc_x'].min()\n",
    "y_range = validation_results['ifc_y'].max() - validation_results['ifc_y'].min()\n",
    "\n",
    "print(f\"\\nSite dimensions: {x_range:.1f}m x {y_range:.1f}m\")\n",
    "\n",
    "# Define edge zones (outer 25% of site)\n",
    "edge_buffer = 0.25\n",
    "x_min_edge = validation_results['ifc_x'].min() + edge_buffer * x_range\n",
    "x_max_edge = validation_results['ifc_x'].max() - edge_buffer * x_range\n",
    "y_min_edge = validation_results['ifc_y'].min() + edge_buffer * y_range\n",
    "y_max_edge = validation_results['ifc_y'].max() - edge_buffer * y_range\n",
    "\n",
    "# Classify points as edge or center\n",
    "edge_mask = (\n",
    "    (validation_results['ifc_x'] < x_min_edge) | \n",
    "    (validation_results['ifc_x'] > x_max_edge) |\n",
    "    (validation_results['ifc_y'] < y_min_edge) | \n",
    "    (validation_results['ifc_y'] > y_max_edge)\n",
    ")\n",
    "\n",
    "edge_data = validation_results[edge_mask]\n",
    "center_data = validation_results[~edge_mask]\n",
    "\n",
    "edge_error = edge_data['distance_m'].mean()\n",
    "center_error = center_data['distance_m'].mean()\n",
    "edge_ratio = edge_error / center_error if center_error > 0 else 0\n",
    "\n",
    "print(\"\\nTest 2: Edge vs Center Error Analysis\")\n",
    "print(\"(GCP deficiency typically shows higher errors at site boundaries)\")\n",
    "print(f\"  Edge zone points: {len(edge_data)} (outer {edge_buffer*100:.0f}% of site)\")\n",
    "print(f\"  Center zone points: {len(center_data)} (inner {(1-2*edge_buffer)*100:.0f}% of site)\")\n",
    "print(f\"  Edge zone mean error: {edge_error:.3f}m\")\n",
    "print(f\"  Center zone mean error: {center_error:.3f}m\")\n",
    "print(f\"  Edge/Center error ratio: {edge_ratio:.2f}\")\n",
    "\n",
    "# Interpretation\n",
    "if edge_ratio > 1.5:\n",
    "    edge_interpretation = \"STRONG edge effect - SUGGESTS GCP deficiency\"\n",
    "    edge_score = 3\n",
    "elif edge_ratio > 1.3:\n",
    "    edge_interpretation = \"MODERATE edge effect - POSSIBLE GCP issue\"\n",
    "    edge_score = 2\n",
    "elif edge_ratio > 1.1:\n",
    "    edge_interpretation = \"WEAK edge effect - MINIMAL GCP signature\"\n",
    "    edge_score = 1\n",
    "else:\n",
    "    edge_interpretation = \"NO edge effect - UNLIKELY GCP issue\"\n",
    "    edge_score = 0\n",
    "\n",
    "print(f\"  Interpretation: {edge_interpretation}\")\n",
    "print(f\"  GCP Score from edge test: {edge_score}/3\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Grading/Slope Diagnostic Tests\n",
    "\n",
    "Perform statistical tests to identify signatures of site grading or slope effects."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test 3: Spatial Correlation Analysis (Grading signature)\n",
    "x_dx_corr, x_dx_p = pearsonr(validation_results['ifc_x'], validation_results['dx'])\n",
    "y_dx_corr, y_dx_p = pearsonr(validation_results['ifc_y'], validation_results['dx'])\n",
    "x_dy_corr, x_dy_p = pearsonr(validation_results['ifc_x'], validation_results['dy'])\n",
    "y_dy_corr, y_dy_p = pearsonr(validation_results['ifc_y'], validation_results['dy'])\n",
    "\n",
    "print(\"\\nTest 3: Spatial Correlation Analysis\")\n",
    "print(\"(Grading/slope typically shows strong correlation between position and bias)\")\n",
    "print(f\"  X-position vs X-bias correlation: r = {x_dx_corr:.3f} (p = {x_dx_p:.6f})\")\n",
    "print(f\"  Y-position vs X-bias correlation: r = {y_dx_corr:.3f} (p = {y_dx_p:.6f})\")\n",
    "print(f\"  X-position vs Y-bias correlation: r = {x_dy_corr:.3f} (p = {x_dy_p:.6f})\")\n",
    "print(f\"  Y-position vs Y-bias correlation: r = {y_dy_corr:.3f} (p = {y_dy_p:.6f})\")\n",
    "\n",
    "# Calculate maximum spatial correlation\n",
    "max_spatial_corr = max(abs(x_dx_corr), abs(y_dx_corr), abs(x_dy_corr), abs(y_dy_corr))\n",
    "primary_spatial_corr = max(abs(x_dx_corr), abs(y_dx_corr))  # Focus on X-bias correlations\n",
    "\n",
    "print(f\"  Maximum spatial correlation: {max_spatial_corr:.3f}\")\n",
    "print(f\"  Primary X-bias spatial correlation: {primary_spatial_corr:.3f}\")\n",
    "\n",
    "# Interpretation\n",
    "if primary_spatial_corr > 0.4:\n",
    "    spatial_interpretation = \"STRONG spatial correlation - SUGGESTS grading/slope\"\n",
    "    spatial_score = 3\n",
    "elif primary_spatial_corr > 0.2:\n",
    "    spatial_interpretation = \"MODERATE spatial correlation - POSSIBLE grading effect\"\n",
    "    spatial_score = 2\n",
    "elif primary_spatial_corr > 0.1:\n",
    "    spatial_interpretation = \"WEAK spatial correlation - MINIMAL grading signature\"\n",
    "    spatial_score = 1\n",
    "else:\n",
    "    spatial_interpretation = \"NO spatial correlation - UNLIKELY grading effect\"\n",
    "    spatial_score = 0\n",
    "\n",
    "print(f\"  Interpretation: {spatial_interpretation}\")\n",
    "print(f\"  Grading Score from spatial test: {spatial_score}/3\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test 4: Systematic Bias Magnitude Analysis\n",
    "print(\"\\nTest 4: Systematic Bias Magnitude Analysis\")\n",
    "print(\"(Strong systematic bias suggests grading; weak bias suggests GCP issues)\")\n",
    "print(f\"  Systematic bias magnitude: {bias_magnitude:.3f}m\")\n",
    "print(f\"  Bias direction: {bias_direction:.1f} degrees from East\")\n",
    "print(f\"  X-component: {dx_mean:.3f}m\")\n",
    "print(f\"  Y-component: {dy_mean:.3f}m\")\n",
    "\n",
    "# Interpretation for grading\n",
    "if bias_magnitude > 0.6:\n",
    "    bias_interpretation_grading = \"STRONG systematic bias - CONSISTENT with grading\"\n",
    "    bias_score_grading = 3\n",
    "elif bias_magnitude > 0.3:\n",
    "    bias_interpretation_grading = \"MODERATE systematic bias - POSSIBLE grading effect\"\n",
    "    bias_score_grading = 2\n",
    "elif bias_magnitude > 0.1:\n",
    "    bias_interpretation_grading = \"WEAK systematic bias - MINIMAL grading signature\"\n",
    "    bias_score_grading = 1\n",
    "else:\n",
    "    bias_interpretation_grading = \"NO systematic bias - UNLIKELY grading effect\"\n",
    "    bias_score_grading = 0\n",
    "\n",
    "# Interpretation for GCP (opposite scoring)\n",
    "if bias_magnitude < 0.2:\n",
    "    bias_interpretation_gcp = \"LOW systematic bias - CONSISTENT with GCP deficiency\"\n",
    "    bias_score_gcp = 2\n",
    "elif bias_magnitude < 0.4:\n",
    "    bias_interpretation_gcp = \"MODERATE systematic bias - POSSIBLE GCP issue\"\n",
    "    bias_score_gcp = 1\n",
    "else:\n",
    "    bias_interpretation_gcp = \"HIGH systematic bias - INCONSISTENT with GCP deficiency\"\n",
    "    bias_score_gcp = 0\n",
    "\n",
    "print(f\"  Grading interpretation: {bias_interpretation_grading}\")\n",
    "print(f\"  GCP interpretation: {bias_interpretation_gcp}\")\n",
    "print(f\"  Grading Score from bias test: {bias_score_grading}/3\")\n",
    "print(f\"  GCP Score from bias test: {bias_score_gcp}/2\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test 5: Quadrant Analysis (Grading signature)\n",
    "x_median = validation_results['ifc_x'].median()\n",
    "y_median = validation_results['ifc_y'].median()\n",
    "\n",
    "# Define quadrants\n",
    "quadrants = {\n",
    "    'NE': validation_results[(validation_results['ifc_x'] >= x_median) & (validation_results['ifc_y'] >= y_median)],\n",
    "    'NW': validation_results[(validation_results['ifc_x'] < x_median) & (validation_results['ifc_y'] >= y_median)],\n",
    "    'SE': validation_results[(validation_results['ifc_x'] >= x_median) & (validation_results['ifc_y'] < y_median)],\n",
    "    'SW': validation_results[(validation_results['ifc_x'] < x_median) & (validation_results['ifc_y'] < y_median)]\n",
    "}\n",
    "\n",
    "print(\"\\nTest 5: Quadrant Analysis\")\n",
    "print(\"(Grading typically shows progressive bias variation across site)\")\n",
    "print(f\"Site divided at median coordinates: ({x_median:.1f}, {y_median:.1f})\")\n",
    "\n",
    "quadrant_stats = {}\n",
    "for quad_name, quad_data in quadrants.items():\n",
    "    if len(quad_data) > 0:\n",
    "        quad_dx = quad_data['dx'].mean()\n",
    "        quad_dy = quad_data['dy'].mean()\n",
    "        quad_dist = quad_data['distance_m'].mean()\n",
    "        quad_n = len(quad_data)\n",
    "        \n",
    "        quadrant_stats[quad_name] = {\n",
    "            'dx': quad_dx,\n",
    "            'dy': quad_dy,\n",
    "            'distance': quad_dist,\n",
    "            'count': quad_n\n",
    "        }\n",
    "        \n",
    "        print(f\"  {quad_name} Quadrant: dx={quad_dx:.3f}m, dy={quad_dy:.3f}m, distance={quad_dist:.3f}m, n={quad_n}\")\n",
    "\n",
    "# Calculate quadrant variation (grading signature)\n",
    "dx_values = [stats['dx'] for stats in quadrant_stats.values()]\n",
    "dy_values = [stats['dy'] for stats in quadrant_stats.values()]\n",
    "dx_range = max(dx_values) - min(dx_values)\n",
    "dy_range = max(dy_values) - min(dy_values)\n",
    "max_quadrant_variation = max(dx_range, dy_range)\n",
    "\n",
    "print(f\"\\n  X-bias range across quadrants: {dx_range:.3f}m\")\n",
    "print(f\"  Y-bias range across quadrants: {dy_range:.3f}m\")\n",
    "print(f\"  Maximum quadrant variation: {max_quadrant_variation:.3f}m\")\n",
    "\n",
    "# Interpretation\n",
    "if max_quadrant_variation > 1.0:\n",
    "    quadrant_interpretation = \"STRONG quadrant variation - SUGGESTS grading/slope\"\n",
    "    quadrant_score = 3\n",
    "elif max_quadrant_variation > 0.5:\n",
    "    quadrant_interpretation = \"MODERATE quadrant variation - POSSIBLE grading effect\"\n",
    "    quadrant_score = 2\n",
    "elif max_quadrant_variation > 0.2:\n",
    "    quadrant_interpretation = \"WEAK quadrant variation - MINIMAL grading signature\"\n",
    "    quadrant_score = 1\n",
    "else:\n",
    "    quadrant_interpretation = \"NO quadrant variation - UNLIKELY grading effect\"\n",
    "    quadrant_score = 0\n",
    "\n",
    "print(f\"  Interpretation: {quadrant_interpretation}\")\n",
    "print(f\"  Grading Score from quadrant test: {quadrant_score}/3\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Comprehensive Scoring and Analysis\n",
    "\n",
    "Calculate final diagnostic scores and determine the most likely cause."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Calculate total scores\n",
    "total_gcp_score = radial_score + edge_score + bias_score_gcp\n",
    "total_grading_score = spatial_score + bias_score_grading + quadrant_score\n",
    "max_gcp_score = 3 + 3 + 2  # radial + edge + bias\n",
    "max_grading_score = 3 + 3 + 3  # spatial + bias + quadrant\n",
    "\n",
    "gcp_percentage = (total_gcp_score / max_gcp_score) * 100\n",
    "grading_percentage = (total_grading_score / max_grading_score) * 100\n",
    "\n",
    "print(\"\\n\" + \"=\"*60)\n",
    "print(\"COMPREHENSIVE DIAGNOSTIC SCORING\")\n",
    "print(\"=\"*60)\n",
    "\n",
    "print(\"\\nGCP DEFICIENCY INDICATORS:\")\n",
    "print(f\"  Radial pattern test: {radial_score}/3\")\n",
    "print(f\"  Edge effect test: {edge_score}/3\")\n",
    "print(f\"  Systematic bias test: {bias_score_gcp}/2\")\n",
    "print(f\"  TOTAL GCP SCORE: {total_gcp_score}/{max_gcp_score} ({gcp_percentage:.1f}%)\")\n",
    "\n",
    "print(\"\\nGRADING/SLOPE INDICATORS:\")\n",
    "print(f\"  Spatial correlation test: {spatial_score}/3\")\n",
    "print(f\"  Systematic bias test: {bias_score_grading}/3\")\n",
    "print(f\"  Quadrant variation test: {quadrant_score}/3\")\n",
    "print(f\"  TOTAL GRADING SCORE: {total_grading_score}/{max_grading_score} ({grading_percentage:.1f}%)\")\n",
    "\n",
    "# Determine primary cause\n",
    "score_difference = grading_percentage - gcp_percentage\n",
    "\n",
    "print(\"\\n\" + \"=\"*60)\n",
    "print(\"DIAGNOSTIC CONCLUSION\")\n",
    "print(\"=\"*60)\n",
    "\n",
    "if score_difference > 30:\n",
    "    conclusion = \"GRADING/SLOPE is the PRIMARY cause\"\n",
    "    confidence = \"HIGH\"\n",
    "elif score_difference > 15:\n",
    "    conclusion = \"GRADING/SLOPE is the LIKELY cause\"\n",
    "    confidence = \"MODERATE\"\n",
    "elif score_difference > -15:\n",
    "    conclusion = \"BOTH factors may contribute\"\n",
    "    confidence = \"LOW\"\n",
    "elif score_difference > -30:\n",
    "    conclusion = \"GCP DEFICIENCY is the LIKELY cause\"\n",
    "    confidence = \"MODERATE\"\n",
    "else:\n",
    "    conclusion = \"GCP DEFICIENCY is the PRIMARY cause\"\n",
    "    confidence = \"HIGH\"\n",
    "\n",
    "print(f\"\\nFINAL CONCLUSION: {conclusion}\")\n",
    "print(f\"CONFIDENCE LEVEL: {confidence}\")\n",
    "print(f\"SCORE DIFFERENCE: {score_difference:.1f} percentage points\")\n",
    "\n",
    "# Create summary statistics\n",
    "diagnostic_summary = {\n",
    "    'timestamp': datetime.now().isoformat(),\n",
    "    'site_name': site_name,\n",
    "    'systematic_bias': {\n",
    "        'magnitude_m': float(bias_magnitude),\n",
    "        'direction_deg': float(bias_direction),\n",
    "        'x_component_m': float(dx_mean),\n",
    "        'y_component_m': float(dy_mean)\n",
    "    },\n",
    "    'gcp_indicators': {\n",
    "        'radial_correlation': float(center_dist_corr),\n",
    "        'edge_center_ratio': float(edge_ratio),\n",
    "        'total_score': int(total_gcp_score),\n",
    "        'max_score': int(max_gcp_score),\n",
    "        'percentage': float(gcp_percentage)\n",
    "    },\n",
    "    'grading_indicators': {\n",
    "        'spatial_correlation_x': float(x_dx_corr),\n",
    "        'spatial_correlation_y': float(y_dx_corr),\n",
    "        'quadrant_variation_m': float(max_quadrant_variation),\n",
    "        'total_score': int(total_grading_score),\n",
    "        'max_score': int(max_grading_score),\n",
    "        'percentage': float(grading_percentage)\n",
    "    },\n",
    "    'conclusion': {\n",
    "        'primary_cause': conclusion,\n",
    "        'confidence_level': confidence,\n",
    "        'score_difference': float(score_difference)\n",
    "    }\n",
    "}\n",
    "\n",
    "print(f\"\\nDiagnostic analysis complete. Summary saved to results.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Visualization\n",
    "\n",
    "Create visualizations to illustrate the diagnostic analysis."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "if enable_visualization:\n",
    "    # Set up plotting style\n",
    "    plt.style.use('default')\n",
    "    sns.set_palette(\"husl\")\n",
    "    \n",
    "    # Create comprehensive diagnostic plot\n",
    "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n",
    "    fig.suptitle(f'GCP vs Grading Diagnostic Analysis - {site_name.upper()}', fontsize=16, fontweight='bold')\n",
    "    \n",
    "    # 1. Radial pattern analysis\n",
    "    axes[0, 0].scatter(validation_results_copy['dist_from_center'], validation_results_copy['distance_m'], \n",
    "                      alpha=0.6, s=10)\n",
    "    z = np.polyfit(validation_results_copy['dist_from_center'], validation_results_copy['distance_m'], 1)\n",
    "    p = np.poly1d(z)\n",
    "    axes[0, 0].plot(validation_results_copy['dist_from_center'], p(validation_results_copy['dist_from_center']), \n",
    "                   \"r--\", alpha=0.8, linewidth=2)\n",
    "    axes[0, 0].set_xlabel('Distance from Site Center (m)')\n",
    "    axes[0, 0].set_ylabel('Total Error (m)')\n",
    "    axes[0, 0].set_title(f'Radial Pattern Test\\nr = {center_dist_corr:.3f}')\n",
    "    axes[0, 0].grid(True, alpha=0.3)\n",
    "    \n",
    "    # 2. Spatial correlation - X position vs X bias\n",
    "    axes[0, 1].scatter(validation_results['ifc_x'], validation_results['dx'], alpha=0.6, s=10)\n",
    "    z = np.polyfit(validation_results['ifc_x'], validation_results['dx'], 1)\n",
    "    p = np.poly1d(z)\n",
    "    axes[0, 1].plot(validation_results['ifc_x'], p(validation_results['ifc_x']), \"r--\", alpha=0.8, linewidth=2)\n",
    "    axes[0, 1].set_xlabel('X Position (m)')\n",
    "    axes[0, 1].set_ylabel('X Bias (m)')\n",
    "    axes[0, 1].set_title(f'Spatial Correlation Test\\nr = {x_dx_corr:.3f}')\n",
    "    axes[0, 1].grid(True, alpha=0.3)\n",
    "    \n",
    "    # 3. Edge vs center comparison\n",
    "    edge_center_data = ['Edge Zone', 'Center Zone']\n",
    "    edge_center_errors = [edge_error, center_error]\n",
    "    colors = ['lightcoral', 'lightblue']\n",
    "    \n",
    "    bars = axes[0, 2].bar(edge_center_data, edge_center_errors, color=colors, alpha=0.8, edgecolor='black')\n",
    "    axes[0, 2].set_ylabel('Mean Error (m)')\n",
    "    axes[0, 2].set_title(f'Edge vs Center Analysis\\nRatio = {edge_ratio:.2f}')\n",
    "    axes[0, 2].grid(True, alpha=0.3, axis='y')\n",
    "    \n",
    "    # Add value labels on bars\n",
    "    for bar, value in zip(bars, edge_center_errors):\n",
    "        axes[0, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,\n",
    "                       f'{value:.3f}m', ha='center', va='bottom', fontweight='bold')\n",
    "    \n",
    "    # 4. Quadrant analysis\n",
    "    quad_names = list(quadrant_stats.keys())\n",
    "    quad_dx_values = [quadrant_stats[q]['dx'] for q in quad_names]\n",
    "    \n",
    "    bars = axes[1, 0].bar(quad_names, quad_dx_values, alpha=0.8, edgecolor='black')\n",
    "    axes[1, 0].set_ylabel('Mean X Bias (m)')\n",
    "    axes[1, 0].set_title(f'Quadrant Variation\\nRange = {dx_range:.3f}m')\n",
    "    axes[1, 0].grid(True, alpha=0.3, axis='y')\n",
    "    \n",
    "    # Color bars by bias magnitude\n",
    "    for bar, value in zip(bars, quad_dx_values):\n",
    "        if value < -0.5:\n",
    "            bar.set_color('red')\n",
    "        elif value < 0:\n",
    "            bar.set_color('orange')\n",
    "        else:\n",
    "            bar.set_color('green')\n",
    "        \n",
    "        axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02 if value >= 0 else bar.get_height() - 0.05,\n",
    "                       f'{value:.3f}', ha='center', va='bottom' if value >= 0 else 'top', fontweight='bold')\n",
    "    \n",
    "    # 5. Diagnostic scores comparison\n",
    "    score_categories = ['GCP\\nDeficiency', 'Grading/\\nSlope']\n",
    "    score_percentages = [gcp_percentage, grading_percentage]\n",
    "    colors = ['lightcoral', 'lightgreen']\n",
    "    \n",
    "    bars = axes[1, 1].bar(score_categories, score_percentages, color=colors, alpha=0.8, edgecolor='black')\n",
    "    axes[1, 1].set_ylabel('Diagnostic Score (%)')\n",
    "    axes[1, 1].set_title('Diagnostic Score Comparison')\n",
    "    axes[1, 1].set_ylim(0, 100)\n",
    "    axes[1, 1].grid(True, alpha=0.3, axis='y')\n",
    "    \n",
    "    # Add value labels on bars\n",
    "    for bar, value in zip(bars, score_percentages):\n",
    "        axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2,\n",
    "                       f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n",
    "    \n",
    "    # 6. Summary text\n",
    "    axes[1, 2].axis('off')\n",
    "    summary_text = f\"\"\"\n",
    "    DIAGNOSTIC SUMMARY\n",
    "    \n",
    "    Systematic Bias: {bias_magnitude:.3f}m\n",
    "    Direction: {bias_direction:.1f}° from East\n",
    "    \n",
    "    GCP Score: {total_gcp_score}/{max_gcp_score} ({gcp_percentage:.1f}%)\n",
    "    - Radial pattern: {radial_score}/3\n",
    "    - Edge effect: {edge_score}/3\n",
    "    - Bias magnitude: {bias_score_gcp}/2\n",
    "    \n",
    "    Grading Score: {total_grading_score}/{max_grading_score} ({grading_percentage:.1f}%)\n",
    "    - Spatial correlation: {spatial_score}/3\n",
    "    - Bias magnitude: {bias_score_grading}/3\n",
    "    - Quadrant variation: {quadrant_score}/3\n",
    "    \n",
    "    CONCLUSION:\n",
    "    {conclusion}\n",
    "    \n",
    "    CONFIDENCE: {confidence}\n",
    "    \"\"\"\n",
    "    axes[1, 2].text(0.1, 0.9, summary_text, transform=axes[1, 2].transAxes, \n",
    "                   fontsize=10, verticalalignment='top', fontfamily='monospace',\n",
    "                   bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    \n",
    "    if save_results:\n",
    "        plot_path = output_path / f\"{site_name}_gcp_vs_grading_analysis.png\"\n",
    "        plt.savefig(plot_path, dpi=300, bbox_inches='tight')\n",
    "        print(f\"Saved diagnostic plot: {plot_path}\")\n",
    "    \n",
    "    plt.show()\n",
    "else:\n",
    "    print(\"Visualization disabled\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. Save Results\n",
    "\n",
    "Save the diagnostic analysis results to files."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "if save_results:\n",
    "    # Save diagnostic summary JSON\n",
    "    summary_json_path = output_path / f\"{site_name}_gcp_vs_grading_diagnostic.json\"\n",
    "    with open(summary_json_path, 'w') as f:\n",
    "        json.dump(diagnostic_summary, f, indent=2)\n",
    "    print(f\"Saved diagnostic summary: {summary_json_path}\")\n",
    "    \n",
    "    # Save detailed report\n",
    "    report_path = output_path / f\"{site_name}_gcp_vs_grading_report.txt\"\n",
    "    with open(report_path, 'w') as f:\n",
    "        f.write(f\"GCP vs Grading Diagnostic Analysis Report\\n\")\n",
    "        f.write(f\"Site: {site_name}\\n\")\n",
    "        f.write(f\"Timestamp: {diagnostic_summary['timestamp']}\\n\")\n",
    "        f.write(f\"\\n\" + \"=\"*60 + \"\\n\")\n",
    "        \n",
    "        f.write(f\"SYSTEMATIC BIAS ANALYSIS\\n\")\n",
    "        f.write(f\"Magnitude: {bias_magnitude:.3f}m\\n\")\n",
    "        f.write(f\"Direction: {bias_direction:.1f} degrees from East\\n\")\n",
    "        f.write(f\"X-component: {dx_mean:.3f}m\\n\")\n",
    "        f.write(f\"Y-component: {dy_mean:.3f}m\\n\")\n",
    "        f.write(f\"\\n\")\n",
    "        \n",
    "        f.write(f\"GCP DEFICIENCY INDICATORS\\n\")\n",
    "        f.write(f\"Radial correlation: {center_dist_corr:.3f}\\n\")\n",
    "        f.write(f\"Edge/center ratio: {edge_ratio:.2f}\\n\")\n",
    "        f.write(f\"Total score: {total_gcp_score}/{max_gcp_score} ({gcp_percentage:.1f}%)\\n\")\n",
    "        f.write(f\"\\n\")\n",
    "        \n",
    "        f.write(f\"GRADING/SLOPE INDICATORS\\n\")\n",
    "        f.write(f\"X-position correlation: {x_dx_corr:.3f}\\n\")\n",
    "        f.write(f\"Y-position correlation: {y_dx_corr:.3f}\\n\")\n",
    "        f.write(f\"Quadrant variation: {max_quadrant_variation:.3f}m\\n\")\n",
    "        f.write(f\"Total score: {total_grading_score}/{max_grading_score} ({grading_percentage:.1f}%)\\n\")\n",
    "        f.write(f\"\\n\")\n",
    "        \n",
    "        f.write(f\"CONCLUSION\\n\")\n",
    "        f.write(f\"Primary cause: {conclusion}\\n\")\n",
    "        f.write(f\"Confidence level: {confidence}\\n\")\n",
    "        f.write(f\"Score difference: {score_difference:.1f} percentage points\\n\")\n",
    "    \n",
    "    print(f\"Saved detailed report: {report_path}\")\n",
    "    \n",
    "    print(f\"\\nAll diagnostic results saved to: {output_path}\")\n",
    "else:\n",
    "    print(\"Save results disabled\")\n",
    "\n",
    "print(f\"\\nGCP vs Grading Diagnostic Analysis Complete!\")\n",
    "print(f\"Final Conclusion: {conclusion}\")\n",
    "print(f\"Confidence Level: {confidence}\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
