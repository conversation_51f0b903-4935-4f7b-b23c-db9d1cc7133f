import numpy as np
import pandas as pd
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
from datetime import datetime
import json
from tabulate import tabulate
import seaborn as sns

print("Imports completed successfully!")

class GeometricPileDetectionConfig:
    """Configuration for geometric C-section pile detection"""
    
    def __init__(self):
        # === Input/Output Paths ===
        self.site_name = "trino_enel"
        self.ground_method = "csf"  # or "pmf", "ransac"


        # === Paths ===
        self.root = self._find_project_root(marker_dir="data")
        self.point_cloud_file = self.root / f"data/output_runs/icp_alignment_corrected/{self.ground_method}/aligned_ifc_{self.ground_method}.ply"
        self.ifc_pile_csv = self.root / "data/processed/trino_enel/advanced_ifc_metadata/advanced_tracker_piles.csv"
        self.output_dir = self.root / f"data/output_runs/pile_detection/geometric_{self.ground_method}"
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # === C-section Physical Dimensions ===
        # === Pile Geometry Parameters (Based on TRJHT56PDP-BF) ===
        self.expected_height = 2.0          # Expected pile height in meters
        self.expected_width = 0.062         # Expected pile width in meters
        self.expected_depth = 0.12          # Expected pile depth in meters
        self.width_tolerance = 0.005        # Allowed variation in width
        self.height_tolerance = 0.5         # Allowed variation in height

        # === Elevation Filtering ===
        # Tighten Z-range to remove tall/noisy objects
        self.min_height = 1.0               # Minimum Z elevation from ground (tighter filter)
        self.max_height = 3.5               # Maximum Z elevation from ground (tighter filter)
        
        # === Clustering Parameters ===
        self.dbscan_eps = 0.5              # Radius for DBSCAN clustering
        self.min_points_per_cluster = 8    # Minimum points in valid cluster
        self.max_cluster_size = 1000         # Ignore overly large noisy clusters
        
        # === Scoring Parameters ===
        self.confidence_threshold = 0.60     # Minimum score to accept detection
        self.height_weight = 0.25            # Weight for height score
        self.width_weight = 0.35             # Weight for width score
        self.aspect_weight = 0.20            # Weight for aspect ratio score
        self.distribution_weight = 0.15      # Weight for point distribution score
        self.density_weight = 0.15           # Weight for density score

        # === Aspect Ratio Constraints ===
        self.min_aspect_ratio = 1.0         # Minimum width/depth ratio
        self.max_aspect_ratio = 6.0         # Maximum width/depth ratio
        
        # === Validation Parameters ===
        self.validation_radius = 3.0        # Radius for matching detections to IFC piles
        self.use_kd_tree_matching = True    # Use k-d tree nearest neighbor instead of radius
        self.max_kd_tree_distance = 5.0     # Maximum distance for k-d tree matching

        # === Multi-Criteria Validation ===
        self.enable_multi_criteria = True
        self.min_criteria_passed = 3        # Must pass at least 3/5 criteria

        # === Grid Constraint Parameters ===
        self.enforce_grid_spacing = False     # Enforce regular grid spacing constraint
        self.expected_grid_spacing = 2.5     # Expected spacing between piles in meters
        self.grid_tolerance = 1.5            # Tolerance for grid alignment

        # === Morphological Filtering ===
        self.enable_morphological_filter = True
        self.min_vertical_density = 0.8     # Points per vertical meter
        self.max_horizontal_spread = 0.3    # Maximum XY spread
 
        # === Visualization Parameters ===
        self.plot_confidence_heatmap = True  # Generate confidence score heatmap
        self.use_overlay_plots = True        # Use overlay visualization instead of side-by-side
        self.expected_pile_count = 14460     # Ground truth pile count from IFC

        # === Debug Parameters ===
        self.debug_mode = True                  #  Enable debug output
        self.save_intermediate_results = True   #  Save clustering results

    
    def _find_project_root(self, marker_dir: str, max_levels: int = 10) -> Path:
        """Finds the project root by looking for a directory containing the given marker (e.g. 'data')"""
        current = Path(__file__).resolve().parent if "__file__" in globals() else Path().resolve()
        print(f"Searching for project root from: {current}")

        expected_subdirs = {"processed", "output_runs"}  # Add more if needed

        for _ in range(max_levels):
            data_dir = current / marker_dir
            if data_dir.is_dir():
                subdirs = {p.name for p in data_dir.iterdir() if p.is_dir()}
                if expected_subdirs.issubset(subdirs):
                    print(f"Found project root at: {current}")
                    return current
            if current == current.parent:
                break
            current = current.parent

        raise FileNotFoundError(f"Could not find project root containing '{marker_dir}' with expected subdirs in any parent.")


# Initialize configuration
config = GeometricPileDetectionConfig()
print(f"Configuration initialized:")
print(f"  Site: {config.site_name}")
print(f"  Ground method: {config.ground_method}")
print(f"  Expected piles: {config.expected_pile_count}")
print(f"  Output directory: {config.output_dir}")
print(f" Debug mode: {config.debug_mode}")

print("Loading point cloud...")

# Simple existence check and load
if not config.point_cloud_file.exists():
    raise FileNotFoundError(f"Point cloud file not found: {config.point_cloud_file}")

pcd = o3d.io.read_point_cloud(str(config.point_cloud_file))
points = np.asarray(pcd.points)

print(f"Loaded {points.shape[0]:,} points from {config.point_cloud_file}")
print(f"Point cloud bounds:")
print(f"  X: {points[:, 0].min():.1f} - {points[:, 0].max():.1f}")
print(f"  Y: {points[:, 1].min():.1f} - {points[:, 1].max():.1f}")
print(f"  Z: {points[:, 2].min():.1f} - {points[:, 2].max():.1f}")

def load_ifc_ground_truth(config):
    """Load IFC pile coordinates as ground truth for validation"""
    try:
        if not config.ifc_pile_csv.exists():
            raise FileNotFoundError(f"IFC pile CSV not found: {config.ifc_pile_csv}")

        ifc_piles = pd.read_csv(config.ifc_pile_csv)
        print(f"Loaded {len(ifc_piles)} IFC pile records")
        
        required_cols = ['X', 'Y', 'Z', 'Tag', 'Name']
        missing_cols = [col for col in required_cols if col not in ifc_piles.columns]
        
        if missing_cols:
            raise ValueError(f"Missing required columns in IFC pile CSV: {missing_cols}")

        # Extract coordinates
        pile_coords = ifc_piles[['X', 'Y', 'Z']].values
        pile_ids = ifc_piles['Tag'].values
        pile_names = ifc_piles['Name'].values
        
        print(f"IFC pile coordinate ranges:")
        print(f"  X: {pile_coords[:, 0].min():.1f} - {pile_coords[:, 0].max():.1f}")
        print(f"  Y: {pile_coords[:, 1].min():.1f} - {pile_coords[:, 1].max():.1f}")
        print(f"  Z: {pile_coords[:, 2].min():.1f} - {pile_coords[:, 2].max():.1f}")
        
        ground_truth = {
            'coordinates': pile_coords,
            'ids': pile_ids,
            'names': pile_names,
            'count': len(pile_coords),
            'piles': [
                {'x': float(coord[0]), 'y': float(coord[1]), 'z': float(coord[2]), 
                 'id': str(pile_ids[i]), 'name': str(pile_names[i])}
                for i, coord in enumerate(pile_coords)
            ]
        }

        
        
        return ground_truth
        
    except Exception as e:
        print(f"Error loading IFC ground truth: {e}")
        return None

# Load ground truth
ground_truth = load_ifc_ground_truth(config)
if ground_truth:
    print(f"\nGround truth loaded: {ground_truth['count']} piles")
else:
    print("No ground truth available - proceeding without validation")

def filter_elevated_points(pts, config):
    """Step 1: Filter points with tighter Z-range (elevation banding)"""
    z_min = np.min(pts[:, 2])
    z_max = np.max(pts[:, 2])

    print(f"Point cloud Z range: {z_min:.2f} to {z_max:.2f}")

    # Apply tighter Z-range filter to remove tall/noisy objects
    height_above_ground = pts[:, 2] - z_min
    mask = (height_above_ground >= config.min_height) & (height_above_ground <= config.max_height)
    elevated_pts = pts[mask]
    
    print(f"Elevation banding filter ({config.min_height:.1f}m - {config.max_height:.1f}m):")
    print(f"  Filtered points: {len(elevated_pts):,} / {len(pts):,} ({len(elevated_pts)/len(pts)*100:.1f}%)")
    print(f"  Removed {len(pts) - len(elevated_pts):,} points outside elevation band")
    return elevated_pts

def cluster_vertical_structures(elevated_pts, config):
    """Step 2: Cluster nearby elevated points using DBSCAN"""
    if len(elevated_pts) < config.min_points_per_cluster:
        print(f"Insufficient points for clustering: {len(elevated_pts)} < {config.min_points_per_cluster}")
        return []
    
    print(f"Clustering {len(elevated_pts)} points with eps={config.dbscan_eps}m, min_samples={config.min_points_per_cluster}")

    # Cluster in XY plane only
    labels = DBSCAN(eps=config.dbscan_eps, min_samples=config.min_points_per_cluster).fit_predict(elevated_pts[:, :2])
    
    # Extract clusters
    unique_labels = set(labels)
    clusters = []
    noise_count = np.sum(labels == -1)

    print(f" DBSCAN found {len(unique_labels)-1} clusters and {noise_count} noise points")
    
    for label in unique_labels:
        if label == -1:  # Skip noise points
            continue
        
        cluster_points = elevated_pts[labels == label]
        
        # Filter by cluster size
        if config.min_points_per_cluster <= len(cluster_points) <= config.max_cluster_size:
            clusters.append(cluster_points)
    
    print(f"Found {len(clusters)} valid clusters from {len(unique_labels)-1} total clusters")
    if clusters:
        cluster_sizes = [len(c) for c in clusters]
        print(f"  Cluster sizes: min={min(cluster_sizes)}, max={max(cluster_sizes)}, avg={np.mean(cluster_sizes):.1f}")
    
    return clusters

def is_regular_spacing(coords, expected_spacing, tolerance):
    """Check if sorted coordinates are spaced regularly within a tolerance."""
    spacings = np.diff(np.sort(coords))
    if len(spacings) == 0:
        return False
    avg_spacing = np.mean(spacings)
    return np.abs(avg_spacing - expected_spacing) < tolerance

def find_grid_aligned_indices(centers, axis, config):
    """
    Find indices of points aligned in regular grid lines along the specified axis ('x' or 'y').
    """
    aligned_indices = set()
    primary_idx = 0 if axis == 'x' else 1

    for i, center in enumerate(centers):
        coord = center[primary_idx]
        nearby = np.abs(centers[:, primary_idx] - coord) < config.grid_tolerance

        if np.sum(nearby) >= 3:
            line_centers = centers[nearby]
            orthogonal_coords = line_centers[:, 1 - primary_idx]

            if is_regular_spacing(orthogonal_coords, config.expected_grid_spacing, config.grid_tolerance):
                aligned_indices.update(np.where(nearby)[0])
    
    return aligned_indices

def enforce_grid_spacing_constraint(clusters, config):
    """Step 2.5: Filter clusters based on regular grid spacing constraint."""
    if not config.enforce_grid_spacing or len(clusters) < 3:
        return clusters

    centers = np.array([cluster.mean(axis=0)[:2] for cluster in clusters])

    horizontal_indices = find_grid_aligned_indices(centers, axis='y', config=config)
    vertical_indices = find_grid_aligned_indices(centers, axis='x', config=config)

    grid_aligned_indices = horizontal_indices.union(vertical_indices)

    if grid_aligned_indices:
        filtered_clusters = [clusters[i] for i in sorted(grid_aligned_indices)]
        print(f"Grid spacing constraint: kept {len(filtered_clusters)} / {len(clusters)} clusters")
        return filtered_clusters
    else:
        print(f"Grid spacing constraint: no grid-aligned clusters found, keeping all {len(clusters)} clusters")
        return clusters

def calculate_distribution_score(pts):
    """Evaluate how linearly or elongated the points are using PCA"""
    if len(pts) < 10:
        return 0.5  # Neutral score for small clusters
    
    try:
        # PCA on XY coordinates
        pca = PCA(n_components=2)
        pca.fit(pts[:, :2])
        
        # Ratio of explained variance
        var_ratio = pca.explained_variance_ratio_
        
        # For C-sections, we expect some elongation but not extreme
        # Good score for moderate elongation (0.6-0.8 ratio)
        primary_var = var_ratio[0]
        if 0.6 <= primary_var <= 0.8:
            return 0.8
        elif 0.5 <= primary_var <= 0.9:
            return 0.6
        else:
            return 0.3
    except:
        return 0.5


def analyze_cluster(cluster, config):
    """Analyze a cluster's shape and point distribution for C-section characteristics."""

    import numpy as np

    # Calculate spatial extents
    x_span = np.ptp(cluster[:, 0])
    y_span = np.ptp(cluster[:, 1])
    z_span = np.ptp(cluster[:, 2])

    # Horizontal dimensions
    max_horizontal = max(x_span, y_span)
    min_horizontal = min(x_span, y_span)
    aspect_ratio = max_horizontal / (min_horizontal + 1e-6)

    # === Hard width filter ===
    width_diff = abs(max_horizontal - config.expected_width)
    if width_diff > config.width_tolerance:
        if config.debug_mode:
            print(f"[REJECTED] Cluster width {max_horizontal:.3f}m deviates from expected {config.expected_width}m (> tol {config.width_tolerance}m)")
        return None

    # === CRITERION 1: Height Score ===
    height_diff = abs(z_span - config.expected_height)
    height_score = max(0, 1.0 - (height_diff / config.height_tolerance))
    height_pass = height_score >= config.height_weight

    # === CRITERION 2: Width Score ===
    width_score = max(0, 1.0 - (width_diff / config.width_tolerance))
    width_pass = width_score >= config.width_weight

    # === CRITERION 3: Aspect Ratio ===
    if config.min_aspect_ratio <= aspect_ratio <= config.max_aspect_ratio:
        aspect_score = 0.8
    elif 0.5 <= aspect_ratio <= 8.0:
        aspect_score = 0.6
    else:
        aspect_score = 0.3
    aspect_pass = aspect_score >= config.aspect_weight

    # === CRITERION 4: Point Density ===
    volume = max(x_span * y_span * z_span, 1e-6)  # prevent div by 0
    density = len(cluster) / volume
    density_score = min(density / 100.0, 1.0)
    density_pass = density_score >= config.density_weight

    # === CRITERION 5: Vertical Distribution ===
    vertical_density = len(cluster) / max(z_span, 1e-6)
    vertical_density_score = min(vertical_density / 100.0, 1.0)
    vertical_density_pass = vertical_density_score >= config.density_weight

    # === CRITERION 6: Horizontal Compactness ===
    center_xy = np.mean(cluster[:, :2], axis=0)
    distances_xy = np.linalg.norm(cluster[:, :2] - center_xy, axis=1)
    max_spread = np.max(distances_xy)
    compactness_score = max(0, 1.0 - (max_spread / config.max_horizontal_spread))
    compactness_pass = max_spread < config.max_horizontal_spread

    # === Combined Confidence ===
    confidence = (
        height_score * config.height_weight +
        width_score * config.width_weight +
        aspect_score * config.aspect_weight +
        vertical_density_score * config.distribution_weight +
        density_score * config.density_weight +
        compactness_score * config.density_weight
    )

    if config.debug_mode:
        print(
            f"[ACCEPTED] Cluster shape → W: {max_horizontal:.3f}m, D: {min_horizontal:.3f}m, H: {z_span:.3f}m | "
            f"Scores → Width: {width_score:.2f}, Aspect: {aspect_ratio:.2f}, Conf: {confidence:.2f}"
        )

    return {
        'confidence': confidence,
        'height_score': height_score,
        'width_score': width_score,
        'aspect_score': aspect_score,
        'density_score': density_score,
        'distribution_score': vertical_density_score,
        'compactness_score': compactness_score,
        'dimensions': {
            'width': max_horizontal,
            'depth': min_horizontal,
            'height': z_span,
            'aspect_ratio': aspect_ratio,
            'density': density,
            'vertical_density': vertical_density,
            'max_spread': max_spread
        },
        'point_count': len(cluster)
    }

def compute_center(cluster):
    """Compute the centroid of a cluster"""
    return np.mean(cluster, axis=0)

def detect_piles_from_pointcloud(points, config):
    """Main function to detect C-section piles from point cloud."""
    
    print("\n=== GEOMETRIC PILE DETECTION ===")
    print(f"Input points: {len(points):,}")

    # Step 1: Elevation filtering
    elevated_pts = filter_elevated_points(points, config)
    if len(elevated_pts) == 0:
        print("No elevated points found")
        return []

    # Step 2: Clustering
    clusters = cluster_vertical_structures(elevated_pts, config)
    if len(clusters) == 0:
        print("No valid clusters found")
        return []

    # Step 2.5: Grid spacing constraint (optional)
    clusters = enforce_grid_spacing_constraint(clusters, config)
    if len(clusters) == 0:
        print("No grid-aligned clusters found")
        return []

    print(f"\nAnalyzing {len(clusters):,} clusters...")
    
    # Step 3: Analyze clusters
    detections = []
    rejected = 0
    accepted = 0

    for i, cluster in enumerate(clusters):
        analysis = analyze_cluster(cluster, config)

        if analysis is None:
            rejected += 1
            print(f"[REJECTED] Cluster {i}")
            continue

        confidence = analysis.get('confidence', 0.0)
        height_score = analysis.get('height_score', 0.0)
        width_score = analysis.get('width_score', 0.0)
        aspect_score = analysis.get('aspect_score', 0.0)
        distribution_score = analysis.get('distribution_score', 0.0)
        density_score = analysis.get('density_score', 0.0)
        compactness_score = analysis.get('compactness_score', 0.0)

        if confidence >= config.confidence_threshold:
            center = compute_center(cluster)
            detection = {
                'x': float(center[0]),
                'y': float(center[1]),
                'z': float(center[2]),
                'confidence': float(confidence),
                'cluster_id': i,
                'point_count': analysis['point_count'],
                'dimensions': analysis['dimensions'],
                'scores': {
                    'height': height_score,
                    'width': width_score,
                    'aspect': aspect_score,
                    'distribution': distribution_score,
                    'density': density_score,
                    'compactness': compactness_score
                }
            }
            accepted += 1
            print(f"[ACCEPTED] Cluster {i}")
            detections.append(detection)
        else:
            rejected += 1
            if config.debug_mode:
                print(f"[REJECTED] Cluster {i} failed confidence threshold: {analysis['confidence']:.3f} < {config.confidence_threshold:.3f}")

    print(f"\n=== Detection Summary ===")
    print(f"  Valid detections: {len(detections)} / {len(clusters)}")
    print(f"  Rejected clusters: {rejected}")
    print(f"  Detection rate: {len(detections)/len(clusters)*100:.1f}%")


    if detections:
        confidences = np.array([d['confidence'] for d in detections])
        print(f"  Confidence range: {confidences.min():.3f} - {confidences.max():.3f}")
        print(f"  Average confidence: {confidences.mean():.3f} ± {confidences.std():.3f}")
    else:
        print("  No valid detections found.")

    return detections


print("Starting geometric pile detection...")
start_time = datetime.now()

# Run detection
geo_detections = detect_piles_from_pointcloud(points, config)

end_time = datetime.now()
processing_time = (end_time - start_time).total_seconds()

print(f"\n=== DETECTION COMPLETED ===")
print(f"Processing time: {processing_time:.2f} seconds")
print(f"Detected piles: {len(geo_detections)}")
print(f"Expected piles (IFC): {config.expected_pile_count}")
print(f"Detection ratio: {len(geo_detections)/config.expected_pile_count:.3f}")

import matplotlib.pyplot as plt
import numpy as np
import random
from scipy.interpolate import griddata

def plot_confidence_heatmap(detections, config, grid_resolution=50):
    """Plot heatmap of confidence scores over XY plane"""
    if not detections:
        print("No detections to plot heatmap")
        return
    
    # Extract coordinates and confidence scores
    x_coords = [d['x'] for d in detections]
    y_coords = [d['y'] for d in detections]
    confidences = [d['confidence'] for d in detections]
    
    # Create grid for interpolation
    x_min, x_max = min(x_coords), max(x_coords)
    y_min, y_max = min(y_coords), max(y_coords)
    
    xi = np.linspace(x_min, x_max, grid_resolution)
    yi = np.linspace(y_min, y_max, grid_resolution)
    xi_grid, yi_grid = np.meshgrid(xi, yi)
    
    # Interpolate confidence values
    zi = griddata((x_coords, y_coords), confidences, (xi_grid, yi_grid), method='cubic', fill_value=0)
    
    # Plot heatmap
    plt.figure(figsize=(12, 8))
    plt.contourf(xi_grid, yi_grid, zi, levels=20, cmap='viridis', alpha=0.8)
    plt.colorbar(label='Confidence Score')
    
    # Overlay detection points
    scatter = plt.scatter(x_coords, y_coords, c=confidences, cmap='viridis', 
                         s=50, edgecolors='white', linewidth=1)
    
    plt.xlabel('X Coordinate (m)')
    plt.ylabel('Y Coordinate (m)')
    plt.title(f'Confidence Score Heatmap ({len(detections)} detections)')
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    plt.tight_layout()
    plt.show()

plot_confidence_heatmap(geo_detections, config)

def match_detections_kd_tree(detections, ground_truth, config):
    """Match detections to ground truth using k-d tree nearest neighbor"""
    from scipy.spatial import cKDTree
    import numpy as np

    if not ground_truth or len(detections) == 0:
        return [], []

    if 'piles' not in ground_truth:
        print("Error: Ground truth missing 'piles' structure")
        return [], []

    gt_coords = np.array([[pile['x'], pile['y']] for pile in ground_truth['piles']])
    detection_coords = np.array([[d['x'], d['y']] for d in detections])

    # Build k-d tree for ground truth
    tree = cKDTree(gt_coords)

    # Find nearest neighbors
    distances, indices = tree.query(detection_coords, distance_upper_bound=config.max_kd_tree_distance)

    matched_detections = []
    matched_gt_indices = set()

    for i, (dist, gt_idx) in enumerate(zip(distances, indices)):
        if dist < config.max_kd_tree_distance and gt_idx not in matched_gt_indices:
            matched_detections.append({
                'detection_idx': i,
                'gt_idx': gt_idx,
                'distance': dist,
                'detection': detections[i],
                'ground_truth': ground_truth['piles'][gt_idx]
            })
            matched_gt_indices.add(gt_idx)

    matched_detection_indices = {m['detection_idx'] for m in matched_detections}
    false_positives = [detections[i] for i in range(len(detections)) if i not in matched_detection_indices]

    print(f"K-d tree matching: {len(matched_detections)} matches, {len(false_positives)} false positives")

    return matched_detections, false_positives

matched_detections, false_positives = match_detections_kd_tree(geo_detections, ground_truth, config)
print(f"Matched detections: {len(matched_detections)}")
print(f"False positives: {len(false_positives)}")




def plot_detection_vs_ifc_xy(detections, validation_results, ground_truth, sample_size=1000):
    """Plot false-positive detection centers vs IFC pile XY coordinates"""

    matched_indices = {m['detection_idx'] for m in validation_results['matched_detections']}
    false_positives = [d for i, d in enumerate(detections) if i not in matched_indices]

    if ground_truth is None or len(false_positives) == 0:
        print("Nothing to plot: missing ground truth or no false positives.")
        return

    ifc_coords = ground_truth['coordinates']
    false_xy = np.array([[d['x'], d['y']] for d in false_positives])

    # Sample for clarity
    if len(false_xy) > sample_size:
        false_xy = false_xy[random.sample(range(len(false_xy)), sample_size)]

    fig, axes = plt.subplots(1, 2, figsize=(16, 8), sharex=True, sharey=True)

    # Plot false positives
    axes[0].scatter(false_xy[:, 0], false_xy[:, 1], s=5, c='red', alpha=0.5, label='False Positives')
    axes[0].set_title("False Positive Detections (XY)")
    axes[0].set_xlabel("X")
    axes[0].set_ylabel("Y")
    axes[0].legend()
    axes[0].set_aspect('equal', adjustable='box')

    # Plot IFC coordinates
    axes[1].scatter(ifc_coords[:, 0], ifc_coords[:, 1], s=5, c='blue', alpha=0.5, label='IFC Ground Truth')
    axes[1].set_title("IFC Pile Coordinates (XY)")
    axes[1].set_xlabel("X")
    axes[1].set_ylabel("Y")
    axes[1].legend()
    axes[1].set_aspect('equal', adjustable='box')

    plt.suptitle("False Positives vs IFC Ground Truth (XY Plane)")
    plt.tight_layout()
    plt.show()

#plot_detection_vs_ifc_xy(geo_detections, validation_results, ground_truth)

def plot_overlay_visualization(detections, validation_results, ground_truth, sample_size=1000):
    """Plot overlay visualization with IFC and false positives on same axis"""
    if not ground_truth or not detections:
        print("Cannot create overlay plot: missing data")
        return
    
    # Extract data
    matched_indices = {m['detection_idx'] for m in validation_results['matched_detections']}
    false_positives = [d for i, d in enumerate(detections) if i not in matched_indices]
    true_positives = [d for i, d in enumerate(detections) if i in matched_indices]
    
    # Sample for visualization if needed
    if len(false_positives) > sample_size:
        false_positives = random.sample(false_positives, sample_size)
    
    # Create overlay plot
    plt.figure(figsize=(14, 10))
    
    # Plot IFC ground truth (blue circles)
    ifc_x = [pile['x'] for pile in ground_truth['piles']]
    ifc_y = [pile['y'] for pile in ground_truth['piles']]
    plt.scatter(ifc_x, ifc_y, c='blue', s=100, alpha=0.7, 
               marker='o', label=f'IFC Ground Truth ({len(ifc_x)})', edgecolors='darkblue')
    
    # Plot true positives (green squares)
    if true_positives:
        tp_x = [d['x'] for d in true_positives]
        tp_y = [d['y'] for d in true_positives]
        plt.scatter(tp_x, tp_y, c='green', s=80, alpha=0.8, 
                   marker='s', label=f'True Positives ({len(true_positives)})', edgecolors='darkgreen')
    
    # Plot false positives (red triangles)
    if false_positives:
        fp_x = [d['x'] for d in false_positives]
        fp_y = [d['y'] for d in false_positives]
        plt.scatter(fp_x, fp_y, c='red', s=60, alpha=0.6, 
                   marker='^', label=f'False Positives ({len(fp_x)})', edgecolors='darkred')
    
    plt.xlabel('X Coordinate (m)')
    plt.ylabel('Y Coordinate (m)')
    plt.title('Detection Results Overlay: IFC vs Geometric Detection')
    plt.legend(loc='upper right')
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    plt.tight_layout()
    plt.show()

#plot_overlay_visualization(geo_detections, validation_results, ground_truth)

def validate_detections_against_ifc(detections, ground_truth, config):
    """Validate geometric detections against IFC ground truth"""
    
    if not ground_truth or len(detections) == 0:
        print("Cannot validate: missing ground truth or detections")
        return None
    
    pile_coords = ground_truth['coordinates']
    detection_coords = np.array([[d['x'], d['y'], d['z']] for d in detections])
    
    print(f"\n=== VALIDATION AGAINST IFC GROUND TRUTH ===")
    
    print("DETECTION COORDS")
    print(f"X: {np.min(detection_coords[:, 0]):.2f} - {np.max(detection_coords[:, 0]):.2f}")
    print(f"Y: {np.min(detection_coords[:, 1]):.2f} - {np.max(detection_coords[:, 1]):.2f}")
    print(f"Z: {np.min(detection_coords[:, 2]):.2f} - {np.max(detection_coords[:, 2]):.2f}")

    print("\nIFC COORDS")
    print(f"X: {np.min(pile_coords[:, 0]):.2f} - {np.max(pile_coords[:, 0]):.2f}")
    print(f"Y: {np.min(pile_coords[:, 1]):.2f} - {np.max(pile_coords[:, 1]):.2f}")
    print(f"Z: {np.min(pile_coords[:, 2]):.2f} - {np.max(pile_coords[:, 2]):.2f}")

    print(f"Detections: {len(detections)}")
    print(f"IFC piles: {len(pile_coords)}")
    
    # Find matches within validation radius
    true_positives = 0
    matched_detections = []
    matched_ifc_piles = set()
    
    for i, detection in enumerate(detections):
        det_coord = detection_coords[i]
        
        # Calculate distances to all IFC piles
        #distances = np.linalg.norm(pile_coords - det_coord, axis=1)
        distances = np.linalg.norm(pile_coords[:, :2] - det_coord[:2], axis=1)

        min_distance = np.min(distances)
        closest_pile_idx = np.argmin(distances)
        
        if min_distance <= config.validation_radius:
            true_positives += 1
            matched_detections.append({
                'detection_idx': i,
                'ifc_pile_idx': closest_pile_idx,
                'distance': min_distance,
                'confidence': detection['confidence']
            })
            matched_ifc_piles.add(closest_pile_idx)
    
    false_positives = len(detections) - true_positives
    false_negatives = len(pile_coords) - len(matched_ifc_piles)
    
    # Calculate metrics
    precision = true_positives / len(detections) if len(detections) > 0 else 0
    recall = true_positives / len(pile_coords) if len(pile_coords) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    print(f"\nValidation Results:")
    print(f"  True Positives: {true_positives}")
    print(f"  False Positives: {false_positives}")
    print(f"  False Negatives: {false_negatives}")
    print(f"  Precision: {precision:.3f}")
    print(f"  Recall: {recall:.3f}")
    print(f"  F1-Score: {f1_score:.3f}")
    
    if matched_detections:
        distances = [m['distance'] for m in matched_detections]
        confidences = [m['confidence'] for m in matched_detections]
        print(f"  Average match distance: {np.mean(distances):.2f}m")
        print(f"  Average confidence of matches: {np.mean(confidences):.3f}")
    
    return {
        'true_positives': true_positives,
        'false_positives': false_positives,
        'false_negatives': false_negatives,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'matched_detections': matched_detections,
        'validation_radius': config.validation_radius
    }

if ground_truth and geo_detections:
    # Choose matching method based on configuration
    if config.use_kd_tree_matching:
        print("\n=== K-D TREE MATCHING ===")
        matched_detections, false_positives = match_detections_kd_tree(geo_detections, ground_truth, config)
        
        # Create validation results structure for compatibility
        validation_results = {
            'matched_detections': matched_detections,
            'false_positives': len(false_positives),
            'true_positives': len(matched_detections),
            'false_negatives': len(ground_truth['piles']) - len(matched_detections),
            'precision': len(matched_detections) / len(geo_detections) if geo_detections else 0,
            'recall': len(matched_detections) / len(ground_truth['piles']) if ground_truth['piles'] else 0
        }
        validation_results['f1_score'] = (2 * validation_results['precision'] * validation_results['recall'] / 
                                         (validation_results['precision'] + validation_results['recall'])) if \
                                         (validation_results['precision'] + validation_results['recall']) > 0 else 0
    else:
        print("\n=== RADIUS-BASED MATCHING ===")
        validation_results = validate_detections_against_ifc(geo_detections, ground_truth, config)
    
    # Generate visualizations    
    if config.use_overlay_plots:
        print("\n=== OVERLAY VISUALIZATION ===")
        plot_overlay_visualization(geo_detections, validation_results, ground_truth)
    else:
        print("\n=== SIDE-BY-SIDE VISUALIZATION ===")
        plot_detection_vs_ifc_xy(geo_detections, validation_results, ground_truth)
        
else:
    validation_results = None
    print("Skipping validation - no ground truth or detections available")



def analyze_false_positives(detections, validation_results, config):
    """Analyze characteristics of false positive detections"""
    
    matched_indices = {m['detection_idx'] for m in validation_results['matched_detections']}
    false_positives = [d for i, d in enumerate(detections) if i not in matched_indices]
    true_positives = [d for i, d in enumerate(detections) if i in matched_indices]
    
    if not false_positives:
        print("No false positives to analyze")
        return
    
    print(f"\n=== FALSE POSITIVE ANALYSIS ===")
    print(f"False positives: {len(false_positives)}")
    print(f"True positives: {len(true_positives)}")
    
    # Compare characteristics
    fp_confidences = [d['confidence'] for d in false_positives]
    tp_confidences = [d['confidence'] for d in true_positives]
    
    fp_widths = [d['dimensions']['width'] for d in false_positives]
    tp_widths = [d['dimensions']['width'] for d in true_positives]
    
    fp_heights = [d['dimensions']['height'] for d in false_positives]
    tp_heights = [d['dimensions']['height'] for d in true_positives]
    
    fp_aspects = [d['dimensions']['aspect_ratio'] for d in false_positives]
    tp_aspects = [d['dimensions']['aspect_ratio'] for d in true_positives]
    
    fp_points = [d['point_count'] for d in false_positives]
    tp_points = [d['point_count'] for d in true_positives]
    
    print(f"\nCONFIDENCE SCORES:")
    print(f"  False positives: {np.mean(fp_confidences):.3f} ± {np.std(fp_confidences):.3f}")
    print(f"  True positives:  {np.mean(tp_confidences):.3f} ± {np.std(tp_confidences):.3f}")
    
    print(f"\nWIDTH (m):")
    print(f"  False positives: {np.mean(fp_widths):.3f} ± {np.std(fp_widths):.3f}")
    print(f"  True positives:  {np.mean(tp_widths):.3f} ± {np.std(tp_widths):.3f}")
    print(f"  Expected width: {config.expected_width:.3f}")
    
    print(f"\nHEIGHT (m):")
    print(f"  False positives: {np.mean(fp_heights):.3f} ± {np.std(fp_heights):.3f}")
    print(f"  True positives:  {np.mean(tp_heights):.3f} ± {np.std(tp_heights):.3f}")
    print(f"  Expected height: {config.expected_height:.3f}")
    
    print(f"\nASPECT RATIO:")
    print(f"  False positives: {np.mean(fp_aspects):.3f} ± {np.std(fp_aspects):.3f}")
    print(f"  True positives:  {np.mean(tp_aspects):.3f} ± {np.std(tp_aspects):.3f}")
    
    print(f"\nPOINT COUNT:")
    print(f"  False positives: {np.mean(fp_points):.1f} ± {np.std(fp_points):.1f}")
    print(f"  True positives:  {np.mean(tp_points):.1f} ± {np.std(tp_points):.1f}")
    
    # Identify outliers
    confidence_threshold = np.percentile(tp_confidences, 25)  # Bottom 25% of TP confidences
    width_outliers = [d for d in false_positives if abs(d['dimensions']['width'] - config.expected_width) > 0.05]
    height_outliers = [d for d in false_positives if abs(d['dimensions']['height'] - config.expected_height) > 0.8]
    
    print(f"\nOUTLIER ANALYSIS:")
    print(f"  Width outliers (>5cm from expected): {len(width_outliers)} / {len(false_positives)}")
    print(f"  Height outliers (>80cm from expected): {len(height_outliers)} / {len(false_positives)}")
    
    # Recommendations
    print(f"\nRECOMMENDATIONS:")
    if np.mean(fp_confidences) < np.mean(tp_confidences) - 0.05:
        print(f"  ✓ Raise confidence threshold to {np.percentile(tp_confidences, 25):.3f}")
    
    if len(width_outliers) > len(false_positives) * 0.3:
        print(f"  ✓ Tighten width tolerance to {np.std(tp_widths) * 2:.3f}")
    
    if len(height_outliers) > len(false_positives) * 0.3:
        print(f"  ✓ Tighten height tolerance to {np.std(tp_heights) * 2:.3f}")
    
    # Spatial distribution analysis
    fp_x = [d['x'] for d in false_positives]
    fp_y = [d['y'] for d in false_positives]
    
    # Check if false positives cluster in specific areas
    from scipy.spatial.distance import pdist
    if len(false_positives) > 5:
        fp_coords = np.array([[d['x'], d['y']] for d in false_positives])
        distances = pdist(fp_coords)
        avg_distance = np.mean(distances)
        
        print(f"\nSPATIAL CLUSTERING:")
        print(f"  Average distance between FPs: {avg_distance:.2f}m")
        if avg_distance < 5.0:
            print(f"  ⚠️  False positives are spatially clustered - check for systematic errors")

def plot_characteristics_comparison(detections, validation_results):
    """Plot comparison of true positive vs false positive characteristics"""
    
    matched_indices = {m['detection_idx'] for m in validation_results['matched_detections']}
    false_positives = [d for i, d in enumerate(detections) if i not in matched_indices]
    true_positives = [d for i, d in enumerate(detections) if i in matched_indices]
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Confidence scores
    axes[0,0].hist([d['confidence'] for d in false_positives], alpha=0.7, label='False Positives', bins=20)
    axes[0,0].hist([d['confidence'] for d in true_positives], alpha=0.7, label='True Positives', bins=20)
    axes[0,0].set_xlabel('Confidence Score')
    axes[0,0].set_ylabel('Count')
    axes[0,0].set_title('Confidence Score Distribution')
    axes[0,0].legend()
    
    # Width distribution
    axes[0,1].hist([d['dimensions']['width'] for d in false_positives], alpha=0.7, label='False Positives', bins=20)
    axes[0,1].hist([d['dimensions']['width'] for d in true_positives], alpha=0.7, label='True Positives', bins=20)
    axes[0,1].set_xlabel('Width (m)')
    axes[0,1].set_ylabel('Count')
    axes[0,1].set_title('Width Distribution')
    axes[0,1].legend()
    
    # Height distribution
    axes[1,0].hist([d['dimensions']['height'] for d in false_positives], alpha=0.7, label='False Positives', bins=20)
    axes[1,0].hist([d['dimensions']['height'] for d in true_positives], alpha=0.7, label='True Positives', bins=20)
    axes[1,0].set_xlabel('Height (m)')
    axes[1,0].set_ylabel('Count')
    axes[1,0].set_title('Height Distribution')
    axes[1,0].legend()
    
    # Aspect ratio distribution
    axes[1,1].hist([d['dimensions']['aspect_ratio'] for d in false_positives], alpha=0.7, label='False Positives', bins=20)
    axes[1,1].hist([d['dimensions']['aspect_ratio'] for d in true_positives], alpha=0.7, label='True Positives', bins=20)
    axes[1,1].set_xlabel('Aspect Ratio')
    axes[1,1].set_ylabel('Count')
    axes[1,1].set_title('Aspect Ratio Distribution')
    axes[1,1].legend()
    
    plt.tight_layout()
    plt.show()

# Usage:
analyze_false_positives(geo_detections, validation_results, config)
plot_characteristics_comparison(geo_detections, validation_results)

if geo_detections:
    detections_df = pd.DataFrame([
        {
            'x': d['x'],
            'y': d['y'],
            'z': d['z'],
            'confidence': d['confidence'],
            'point_count': d['point_count'],
            'width': d['dimensions']['width'],
            'depth': d['dimensions']['depth'],
            'height': d['dimensions']['height'],
            'aspect_ratio': d['dimensions']['aspect_ratio'],
            'height_score': d['scores']['height'],
            'width_score': d['scores']['width'],
            'aspect_score': d['scores']['aspect'],
            'distribution_score': d['scores']['distribution']
        }
        for d in geo_detections
    ])
    
    detections_file = config.output_dir / f"geometric_detections_{config.ground_method}.csv"
    detections_df.to_csv(detections_file, index=False)
    print(f"\nSaved {len(geo_detections)} detections to: {detections_file}")
    
    # Display summary statistics
    print(f"\nDetection Summary:")
    print(detections_df[['confidence', 'width', 'height', 'aspect_ratio']].describe())

# Save comprehensive metrics
metrics = {
    'timestamp': datetime.now().isoformat(),
    'site_name': config.site_name,
    'ground_method': config.ground_method,
    'processing_time_seconds': processing_time,
    'input_points': len(points),
    'detected_piles': len(geo_detections),
    'expected_piles': config.expected_pile_count,
    'detection_ratio': len(geo_detections) / config.expected_pile_count,
    'confidence_threshold': config.confidence_threshold,
    'approach': 'geometric_rule_based'
}

if validation_results:
    metrics.update({
        'validation_precision': validation_results['precision'],
        'validation_recall': validation_results['recall'],
        'validation_f1_score': validation_results['f1_score'],
        'true_positives': validation_results['true_positives'],
        'false_positives': validation_results['false_positives'],
        'false_negatives': validation_results['false_negatives']
    })

metrics_file = config.output_dir / f"geometric_metrics_{config.ground_method}.json"
with open(metrics_file, 'w') as f:
    json.dump(metrics, f, indent=2)

print(f"\nSaved metrics to: {metrics_file}")